#!/usr/bin/env node

/**
 * Excella Database Setup Script
 * Automates Supabase database migration and initial configuration
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const MIGRATION_FILE = path.join(__dirname, 'database-migration.sql');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function validateEnvironment() {
  logStep('1/5', 'Validating environment variables...');
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    logError(`Missing required environment variables: ${missing.join(', ')}`);
    log('\nPlease add these to your .env file:', 'yellow');
    missing.forEach(varName => {
      log(`${varName}=your_${varName.toLowerCase()}_here`, 'yellow');
    });
    process.exit(1);
  }
  
  logSuccess('Environment variables validated');
}

async function checkSupabaseConnection() {
  logStep('2/5', 'Testing Supabase connection...');
  
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    
    // Test connection with a simple query
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);
    
    if (error) {
      throw error;
    }
    
    logSuccess('Supabase connection established');
    return supabase;
  } catch (error) {
    logError(`Failed to connect to Supabase: ${error.message}`);
    log('\nPlease check:', 'yellow');
    log('1. Your Supabase URL is correct', 'yellow');
    log('2. Your service role key is valid', 'yellow');
    log('3. Your Supabase project is active', 'yellow');
    process.exit(1);
  }
}

async function readMigrationFile() {
  logStep('3/5', 'Reading migration file...');
  
  try {
    if (!fs.existsSync(MIGRATION_FILE)) {
      throw new Error(`Migration file not found: ${MIGRATION_FILE}`);
    }
    
    const migrationSQL = fs.readFileSync(MIGRATION_FILE, 'utf8');
    logSuccess(`Migration file loaded (${migrationSQL.length} characters)`);
    return migrationSQL;
  } catch (error) {
    logError(`Failed to read migration file: ${error.message}`);
    process.exit(1);
  }
}

async function runMigration(supabase, migrationSQL) {
  logStep('4/5', 'Running database migration...');
  
  try {
    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    log(`Executing ${statements.length} SQL statements...`, 'blue');
    
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        // Execute each statement
        const { error } = await supabase.rpc('exec_sql', { 
          sql: statement + ';' 
        });
        
        if (error) {
          // Some errors are expected (like "table already exists")
          if (error.message.includes('already exists') || 
              error.message.includes('IF NOT EXISTS')) {
            logWarning(`Statement ${i + 1}: ${error.message}`);
          } else {
            throw error;
          }
        }
        
        successCount++;
        
        // Progress indicator
        if ((i + 1) % 10 === 0) {
          log(`Progress: ${i + 1}/${statements.length} statements`, 'blue');
        }
        
      } catch (error) {
        errorCount++;
        logError(`Statement ${i + 1} failed: ${error.message}`);
        
        // Continue with other statements unless it's a critical error
        if (error.message.includes('permission denied') || 
            error.message.includes('authentication')) {
          throw error;
        }
      }
    }
    
    logSuccess(`Migration completed: ${successCount} successful, ${errorCount} errors`);
    
    if (errorCount > 0) {
      logWarning('Some statements failed, but migration continued');
    }
    
  } catch (error) {
    logError(`Migration failed: ${error.message}`);
    
    if (error.message.includes('exec_sql')) {
      logWarning('Direct SQL execution not available. Trying alternative approach...');
      
      // Alternative: Use individual table creation
      await createTablesIndividually(supabase);
    } else {
      throw error;
    }
  }
}

async function createTablesIndividually(supabase) {
  log('Creating tables using Supabase client...', 'blue');
  
  // This is a fallback method if direct SQL execution isn't available
  // In practice, you would use Supabase CLI or dashboard for migrations
  
  logWarning('Please run the migration manually using:');
  log('1. Supabase CLI: supabase db reset', 'yellow');
  log('2. Or copy the SQL from scripts/database-migration.sql to your Supabase dashboard', 'yellow');
}

async function verifyMigration(supabase) {
  logStep('5/5', 'Verifying migration...');
  
  try {
    // Check if core tables exist
    const expectedTables = [
      'users',
      'conversations', 
      'messages',
      'subscriptions',
      'data_connections'
    ];
    
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', expectedTables);
    
    if (error) {
      throw error;
    }
    
    const foundTables = tables.map(t => t.table_name);
    const missingTables = expectedTables.filter(t => !foundTables.includes(t));
    
    if (missingTables.length === 0) {
      logSuccess('All core tables created successfully');
    } else {
      logWarning(`Missing tables: ${missingTables.join(', ')}`);
    }
    
    // Test RLS policies
    log('Testing Row Level Security...', 'blue');
    const { data: policies, error: policyError } = await supabase
      .from('pg_policies')
      .select('tablename, policyname')
      .limit(5);
    
    if (!policyError && policies.length > 0) {
      logSuccess(`RLS policies active (${policies.length} found)`);
    } else {
      logWarning('RLS policies may not be properly configured');
    }
    
  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    logWarning('Migration may have completed with issues');
  }
}

async function main() {
  log('🚀 Excella Database Setup', 'bright');
  log('================================', 'bright');
  
  try {
    await validateEnvironment();
    const supabase = await checkSupabaseConnection();
    const migrationSQL = await readMigrationFile();
    await runMigration(supabase, migrationSQL);
    await verifyMigration(supabase);
    
    log('\n🎉 Database setup completed!', 'green');
    log('\nNext steps:', 'cyan');
    log('1. Configure your tRPC API endpoints', 'blue');
    log('2. Set up authentication in your app', 'blue');
    log('3. Test the API with sample data', 'blue');
    
  } catch (error) {
    log('\n💥 Setup failed!', 'red');
    logError(error.message);
    
    log('\nTroubleshooting:', 'yellow');
    log('1. Check your environment variables', 'yellow');
    log('2. Verify Supabase project is active', 'yellow');
    log('3. Ensure service role key has admin permissions', 'yellow');
    log('4. Try running migration manually in Supabase dashboard', 'yellow');
    
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = {
  validateEnvironment,
  checkSupabaseConnection,
  runMigration,
  verifyMigration
};
