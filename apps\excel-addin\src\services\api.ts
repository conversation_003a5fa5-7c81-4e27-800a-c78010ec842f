// Excella tRPC Router Implementation
// Type-safe API endpoints for Excella MVP
// Based on: .excella/core/data-models-system-architecture-plan.md

import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from './trpc';
import { TRPCError } from '@trpc/server';
import { createClient } from '@supabase/supabase-js';

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

// User schemas
const updateProfileSchema = z.object({
  full_name: z.string().optional(),
  language: z.enum(['en', 'fr']).optional(),
  timezone: z.string().optional(),
  preferred_platform: z.enum(['excel', 'web']).optional(),
  currency: z.enum(['USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS']).optional()
});

// AI model and sandbox schemas
const aiModelSchema = z.enum(['gemini-2.5-pro', 'claude-4-sonnet', 'deepseek-coder']);
const sandboxMethodSchema = z.enum(['pyodide', 'e2b', 'hybrid']);

// Message schemas
const createMessageSchema = z.object({
  conversation_id: z.string().uuid(),
  content: z.string().min(1),
  message_type: z.enum(['text', 'analysis', 'visualization', 'code', 'error']).default('text'),
  model_used: aiModelSchema.optional(),
  sandbox_method: sandboxMethodSchema.optional()
});

// Conversation schemas
const createConversationSchema = z.object({
  title: z.string().optional(),
  platform: z.enum(['excel', 'web']),
  excel_workbook_id: z.string().optional(),
  agent_mode: z.enum(['chat', 'agent']).default('chat')
});

// Data connection schemas
const createDataConnectionSchema = z.object({
  name: z.string().min(1),
  connection_type: z.enum([
    'postgresql', 'mysql', 'mssql', 'oracle', 'supabase',
    'salesforce', 'zoho', 'quickbooks', 'snowflake',
    'onedrive', 'googledrive', 'googlesheets', 'local_file'
  ]),
  connection_config: z.record(z.any()), // Will be encrypted
  access_method: z.enum(['direct', 'mcp', 'hybrid']).default('direct')
});

// =====================================================
// USER MANAGEMENT ROUTER
// =====================================================

export const userRouter = router({
  // Get user profile
  getProfile: protectedProcedure
    .query(async ({ ctx }) => {
      const { data, error } = await ctx.supabase
        .from('users')
        .select('*')
        .eq('id', ctx.user.id)
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch user profile'
        });
      }

      return data;
    }),

  // Update user profile
  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const { data, error } = await ctx.supabase
        .from('users')
        .update({ ...input, updated_at: new Date() })
        .eq('id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update profile'
        });
      }

      return data;
    }),

  // Get usage statistics
  getUsageStats: protectedProcedure
    .query(async ({ ctx }) => {
      const { data: user, error } = await ctx.supabase
        .from('users')
        .select('monthly_queries_used, monthly_queries_limit, subscription_tier')
        .eq('id', ctx.user.id)
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch usage stats'
        });
      }

      const queriesUsed = user.monthly_queries_used || 0;
      const queriesLimit = user.subscription_tier === 'free' ? (user.monthly_queries_limit || 15) : -1; // -1 = unlimited
      
      return {
        queriesUsed,
        queriesLimit,
        tier: user.subscription_tier || 'free',
        usagePercentage: queriesLimit > 0 ? (queriesUsed / queriesLimit) * 100 : 0,
        isUnlimited: queriesLimit === -1
      };
    }),

  // Increment usage (called after AI query)
  incrementUsage: protectedProcedure
    .input(z.object({
      tokensUsed: z.number(),
      costUsd: z.number(),
      modelUsed: aiModelSchema,
      sandboxMethod: sandboxMethodSchema.optional()
    }))
    .mutation(async ({ ctx, input }) => {
      // Check current usage and limits
      const { data: user, error: fetchError } = await ctx.supabase
        .from('users')
        .select('monthly_queries_used, monthly_queries_limit, subscription_tier')
        .eq('id', ctx.user.id)
        .single();

      if (fetchError) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch user data'
        });
      }

      // Check if user has exceeded limits (Free tier only)
      const isLimitExceeded = user.subscription_tier === 'free' && 
                             user.monthly_queries_used >= user.monthly_queries_limit;

      if (isLimitExceeded) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Monthly query limit exceeded. Please upgrade to Professional ($20/month) for unlimited queries.'
        });
      }

      // Increment usage
      const { data, error } = await ctx.supabase
        .from('users')
        .update({ 
          monthly_queries_used: (user.monthly_queries_used || 0) + 1,
          updated_at: new Date()
        })
        .eq('id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update usage'
        });
      }

      return {
        success: true,
        newUsage: data.monthly_queries_used,
        modelUsed: input.modelUsed,
        sandboxMethod: input.sandboxMethod
      };
    }),

  // Complete onboarding
  completeOnboarding: protectedProcedure
    .mutation(async ({ ctx }) => {
      const { data, error } = await ctx.supabase
        .from('users')
        .update({ 
          onboarding_completed: true,
          updated_at: new Date()
        })
        .eq('id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to complete onboarding'
        });
      }

      return data;
    })
});

// =====================================================
// CONVERSATION ROUTER
// =====================================================

export const conversationRouter = router({
  // Get all conversations for user
  getAll: protectedProcedure
    .input(z.object({
      platform: z.enum(['excel', 'web']).optional(),
      limit: z.number().min(1).max(100).default(50),
      offset: z.number().min(0).default(0)
    }))
    .query(async ({ ctx, input }) => {
      let query = ctx.supabase
        .from('conversations')
        .select('*')
        .eq('user_id', ctx.user.id)
        .eq('status', 'active')
        .order('updated_at', { ascending: false })
        .range(input.offset, input.offset + input.limit - 1);

      if (input.platform) {
        query = query.eq('platform', input.platform);
      }

      const { data, error } = await query;

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch conversations'
        });
      }

      return data;
    }),

  // Get single conversation with messages
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      // Get conversation
      const { data: conversation, error: convError } = await ctx.supabase
        .from('conversations')
        .select('*')
        .eq('id', input.id)
        .eq('user_id', ctx.user.id)
        .single();

      if (convError || !conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found'
        });
      }

      // Get messages
      const { data: messages, error: msgError } = await ctx.supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', input.id)
        .order('created_at', { ascending: true });

      if (msgError) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch messages'
        });
      }

      return {
        ...conversation,
        messages: messages || []
      };
    }),

  // Create new conversation
  create: protectedProcedure
    .input(createConversationSchema)
    .mutation(async ({ ctx, input }) => {
      const { data, error } = await ctx.supabase
        .from('conversations')
        .insert({
          ...input,
          user_id: ctx.user.id,
          title: input.title || 'New Conversation'
        })
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create conversation'
        });
      }

      return data;
    }),

  // Update conversation
  update: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      title: z.string().optional(),
      agent_mode: z.enum(['chat', 'agent']).optional(),
      data_sources: z.array(z.string()).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;
      
      const { data, error } = await ctx.supabase
        .from('conversations')
        .update({ ...updateData, updated_at: new Date() })
        .eq('id', id)
        .eq('user_id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update conversation'
        });
      }

      return data;
    }),

  // Archive conversation
  archive: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const { data, error } = await ctx.supabase
        .from('conversations')
        .update({ 
          status: 'archived',
          updated_at: new Date()
        })
        .eq('id', input.id)
        .eq('user_id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to archive conversation'
        });
      }

      return data;
    })
});

// =====================================================
// MESSAGE ROUTER
// =====================================================

export const messageRouter = router({
  // Create new message
  create: protectedProcedure
    .input(createMessageSchema.extend({
      role: z.enum(['user', 'assistant', 'system']),
      agent_name: z.string().optional(),
      tokens_used: z.number().optional(),
      cost_usd: z.number().optional(),
      execution_time_ms: z.number().optional(),
      confidence_score: z.number().min(0).max(1).optional(),
      result_data: z.record(z.any()).optional(),
      attachments: z.array(z.record(z.any())).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      // Verify conversation belongs to user
      const { data: conversation, error: convError } = await ctx.supabase
        .from('conversations')
        .select('id, user_id, message_count, total_tokens_used, total_cost_usd')
        .eq('id', input.conversation_id)
        .eq('user_id', ctx.user.id)
        .single();

      if (convError || !conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found'
        });
      }

      // Create message
      const { data, error } = await ctx.supabase
        .from('messages')
        .insert(input)
        .select()
        .single();

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create message'
        });
      }

      // Update conversation message count and cost tracking
      await ctx.supabase
        .from('conversations')
        .update({
          message_count: (conversation.message_count || 0) + 1,
          total_tokens_used: (conversation.total_tokens_used || 0) + (input.tokens_used || 0),
          total_cost_usd: (conversation.total_cost_usd || 0) + (input.cost_usd || 0),
          updated_at: new Date()
        })
        .eq('id', input.conversation_id);

      return data;
    }),

  // Get messages for conversation
  getByConversation: protectedProcedure
    .input(z.object({
      conversation_id: z.string().uuid(),
      limit: z.number().min(1).max(100).default(50),
      offset: z.number().min(0).default(0)
    }))
    .query(async ({ ctx, input }) => {
      // Verify conversation belongs to user
      const { data: conversation, error: convError } = await ctx.supabase
        .from('conversations')
        .select('id')
        .eq('id', input.conversation_id)
        .eq('user_id', ctx.user.id)
        .single();

      if (convError || !conversation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Conversation not found'
        });
      }

      const { data, error } = await ctx.supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', input.conversation_id)
        .order('created_at', { ascending: true })
        .range(input.offset, input.offset + input.limit - 1);

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch messages'
        });
      }

      return data;
    })
});

// =====================================================
// MAIN APP ROUTER
// =====================================================

export const appRouter = router({
  user: userRouter,
  conversation: conversationRouter,
  message: messageRouter
});

export type AppRouter = typeof appRouter;
