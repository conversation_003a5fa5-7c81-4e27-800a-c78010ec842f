# Contributing Guidelines

*General contributing guidelines for the project*

## Overview

This document outlines the general contributing guidelines for this project. For Excella-specific development information, see [`.excella/setup/package-installation-guide.md`](../.excella/setup/package-installation-guide.md).

## Getting Started

1. **Fork the repository** and clone your fork
2. **Install dependencies** following the setup guide in `.excella/setup/`
3. **Create a feature branch** from `main`
4. **Make your changes** following the coding standards below
5. **Test your changes** thoroughly
6. **Submit a pull request** with a clear description

## Coding Standards

### General Guidelines

- Follow existing code style and patterns
- Write clear, descriptive commit messages
- Include tests for new functionality
- Update documentation when necessary
- Keep changes focused and atomic

### TypeScript/JavaScript

- Use TypeScript for all new code
- Follow ESLint configuration
- Use meaningful variable and function names
- Add JSDoc comments for public APIs

### Documentation

- **General documentation**: Add to `docs/` directory
- **Excella-specific documentation**: Add to `.excella/` directory
- Use clear, concise language
- Include code examples where helpful
- Keep documentation up-to-date with code changes

### File Naming Convention

This project uses **kebab-case** for all file names:
- ✅ `readme.md`
- ✅ `contributing.md`
- ✅ `technical-stack.md`
- ✅ `package-installation-guide.md`
- ❌ `README.md`
- ❌ `CONTRIBUTING.md`
- ❌ `TechnicalStack.md`

## Pull Request Process

1. **Update documentation** if your changes affect user-facing functionality
2. **Add tests** for new features or bug fixes
3. **Ensure all tests pass** before submitting
4. **Request review** from maintainers
5. **Address feedback** promptly and professionally

## Code Review Guidelines

- Be constructive and respectful in feedback
- Focus on code quality, not personal preferences
- Suggest improvements with explanations
- Approve when changes meet project standards

## Questions?

- Check existing documentation in `docs/` and `.excella/`
- Search existing issues and discussions
- Create a new issue for bugs or feature requests
- Reach out to maintainers for guidance

---

*For Excella-specific development setup and technical details, see the `.excella/` directory.*
