# Project Brief: Excella

## Executive Summary

Excella is an intelligent Excel add-in that democratizes advanced data analysis for African markets through a globally competitive product, bringing Julius AI-style capabilities directly into Excel. The system combines natural language processing, voice input, and computer vision to enable intuitive data analysis, statistical modeling, and visualization generation through conversational interfaces.

The project consists of two integrated components: a modern Excel add-in built with React 19.0.0, Shadcn/ui components, and Magic UI animations, plus a comprehensive web application for user management and analytics. Targeting business professionals, SME owners, and organizations across Africa (starting with Ghana), Excella addresses the critical need for accessible, multilingual data analysis tools in emerging markets while maintaining world-class product standards.

## Project Objectives

### Primary Objectives
1. **Democratize Data Analysis**: Enable non-technical users to perform advanced analytics through natural language and voice commands with 90% user satisfaction rate
2. **Product-Market Fit Validation**: Achieve 100-500 active users with 70%+ retention and 4.0+ star rating within 6 months
3. **Multilingual Accessibility**: Provide seamless English and French language support with 95% accuracy in voice recognition
4. **Revenue Generation**: Achieve $90K-675K ARR within 12 months through wallet-based pay-per-use model (validated June 2025)

### Secondary Objectives
1. **Community Building**: Build engaged user community of 100-300 members with WhatsApp groups
2. **Team Market**: Capture SME and team market with volume pricing, admin controls, shared templates, and usage analytics (collaboration features excluded)
3. **Technical Excellence**: Maintain 99% uptime and sub-3-second response times using managed services
4. **Cost Optimization**: Achieve 85-90% gross margins through intelligent AI model routing

## Project Scope

### In Scope
- Excel add-in development with React 19.0.0, Shadcn/ui components, and Magic UI animations
- Web application for user management and analytics built with Next.js 15.x
- Multi-agent AI system using Agno 1.2.2 framework for data analysis and visualization
- Hybrid sandbox strategy combining Pyodide (WebAssembly) and E2B Code Interpreter
- Voice input processing in English and French using OpenAI Whisper API
- Computer vision for OCR and document processing via multi-modal AI models
- Integration with local and cloud file systems
- African market-optimized payment processing (Paystack, Flutterwave) with global backup (Stripe, PayPal)
- Compliance with GDPR and African data protection laws
- Business association partnerships and enterprise sales

### Out of Scope
- Native mobile applications (not applicable to Excel add-ins)
- Integration with non-Microsoft Office suites
- Real-time collaboration features (Phase 2)
- Advanced machine learning model training
- Custom enterprise on-premise deployments
- Support for languages beyond English and French (Phase 2)

## Constraints

### Technical Constraints
- Must work within Excel's add-in security sandbox
- Limited to Office.js API capabilities (ExcelApi 1.17+)
- Supabase regional availability limitations in Africa (Europe region closest)
- Internet connectivity requirements for AI processing (Pyodide enables offline capabilities)
- React 19.0.0 compatibility requirements for all frontend dependencies

### Business Constraints
- Bootstrap budget: $9.5K-17.9K annually for operational costs (validated June 2025 financial analysis)
- Timeline: 6-month MVP delivery requirement
- Team size: Solo developer using AI-assisted development
- Regulatory compliance with Ghana/Nigeria data protection laws

### Market Constraints
- Limited payment processing options in target markets
- Varying internet infrastructure across African regions
- Currency fluctuation and pricing sensitivity
- Competition from established global players

## Success Criteria
*Revised for Solo Developer + AI-Assisted Development Reality*

### Technical Success Metrics (MVP Validation)
- **Performance**: 95% of operations complete within 3 seconds (✅ **ACHIEVABLE** with modern stack)
- **Reliability**: 99% uptime using Vercel/Supabase managed services (✅ **REALISTIC** for solo dev)
- **Accuracy**: 90%+ accuracy in natural language query interpretation (✅ **ACHIEVABLE** with GPT-4/Claude)
- **Security**: Zero data breaches, basic security compliance with Supabase RLS (✅ **PRACTICAL**)

### Business Success Metrics (Wallet-Based Model)
- **User Adoption**: 1,000-10,000 active users within 6 months (✅ **REALISTIC** with pay-per-use accessibility)
- **Revenue**: $90K-1.8M ARR within 12 months (✅ **VALIDATED** June 2025 wallet-based financial model - Conservative: $90K, Realistic: $675K, Optimistic: $1.8M)
- **Unit Economics**: 32% gross margins with real-time cost calculation (✅ **ACHIEVED** with 20-33% markup per validated financial analysis)
- **Product-Market Fit**: 70%+ user retention after 30 days, 4.0+ star rating (✅ **MEASURABLE**)
- **Customer Validation**: 50+ detailed user interviews, 80%+ would recommend to colleagues (✅ **ACTIONABLE**)

### Solo Developer Impact Metrics (Realistic Scope)
- **Technical Validation**: Working Excel add-in with AI capabilities deployed and functional (✅ **CORE GOAL**)
- **User Feedback**: 50+ detailed user feedback sessions with African business professionals (✅ **ACHIEVABLE**)
- **Community Building**: 100-300 engaged users in WhatsApp groups (✅ **MANAGEABLE**)
- **Content Creation**: 20+ tutorial videos, comprehensive documentation, 5+ case studies (✅ **SOLO-FRIENDLY**)

### Success Criteria Rationale (Solo Developer Context)

#### **Why These Metrics Are Realistic:**
- **1,000-10,000 Users**: Achievable through pay-per-use accessibility, no subscription barriers, and African tech communities
- **$90K-1.8M ARR**: Validated through June 2025 wallet-based financial model (Conservative: $90K, Realistic: $675K, Optimistic: $1.8M)
- **32% Gross Margin**: Achieved through 20-33% markup on all queries with optimized AI model routing (validated financial analysis)
- **50+ User Interviews**: Provides deep customer insights for product-market fit validation
- **Technical Validation**: Core proof that AI-powered Excel add-in concept works with wallet-based pricing

#### **Metrics Removed and Why:**
- **SOC II Compliance**: $15K-50K annual cost, requires dedicated compliance team
- **5,000+ Users**: Needs $20K-50K marketing budget or viral growth unlikely for niche B2B tool
- **$100K ARR**: Requires enterprise sales team or 1,000+ users (unrealistic for solo dev in Year 1)
- **50+ Institutional Partnerships**: Needs business development team, travel budget, sales process
- **15% Market Penetration**: Requires market research, competitive analysis, significant resources

#### **Bootstrap Budget Alignment (Updated June 2025):**
- **Total Operational Costs**: $9.5K-17.9K annually (infrastructure $600, AI APIs $8,400-16,800, tools $500) - optimized stack
- **Revenue Target**: $90K-1.8M ARR (Conservative: $90K, Realistic: $675K, Optimistic: $1.8M - validated financial model)
- **Break-even**: Month 1-2 with 67+ active users spending $7.50-15.00/month average (validated financial analysis)
- **Growth Strategy**: Organic growth through pay-per-use accessibility, content, and community
- **Validation Focus**: Product-market fit over scale, profitable unit economics (32% gross margin) on every transaction

## Target Audience

### Primary Users
- **Business Analysts**: Professionals requiring advanced Excel analytics in corporate environments
- **SME Owners**: Small business owners needing data insights without technical expertise
- **Team Leaders**: Department heads and project managers coordinating data analysis across teams

*Note: Excella explicitly excludes students and researchers as target segments, focusing on business professionals and commercial use cases.*


### Geographic Focus
- **Primary Market**: Ghana (English-speaking regions)
- **Secondary Markets**: Côte d'Ivoire, Senegal (French-speaking regions)
- **Expansion Markets**: Nigeria, Kenya, South Africa (Phase 2)

### User Characteristics
- Limited technical background in programming or advanced Excel
- Comfortable with basic Excel operations
- Need for data analysis in local business contexts
- Preference for voice and natural language interfaces
- Price-sensitive with preference for value-based pricing

## Core Features and Requirements

### System Components

#### Excel Add-in (Primary Interface)
- [ ] **Conversational Interface**: Sidebar chat/voice assistant for natural language queries using Shadcn/ui components
- [ ] **Multilingual Support**: Context-aware conversations in English and French with next-intl
- [ ] **Session Management**: Multi-turn analysis with conversation memory via Agno framework
- [ ] **Code Execution**: Hybrid sandbox using Pyodide (WebAssembly) and E2B Code Interpreter
- [ ] **Formula Generation**: Natural language to Excel formula conversion via AI models
- [ ] **Voice Processing**: Voice input processing using OpenAI Whisper API with 95% accuracy
- [ ] **Animated Feedback**: Magic UI components for loading states, progress indicators, and visual feedback

#### Web Application (Management Platform)
- [ ] **User Management**: Authentication via Supabase Auth, profiles, and account management
- [ ] **Team Management**: Workspace administration and team settings management
- [ ] **Analytics Dashboard**: Usage analytics and performance reporting with PostHog integration
- [ ] **Billing System**: Wallet-based payment management with Paystack/Flutterwave payment processing
- [ ] **Configuration**: System settings and user preferences with tRPC type-safe APIs

#### Backend Infrastructure
- [ ] **API Gateway**: tRPC 11.2.0 for type-safe API management and routing
- [ ] **Data Layer**: Supabase PostgreSQL 15.x with Row Level Security and caching
- [ ] **Job Processing**: Supabase Edge Functions with Deno 2.x runtime
- [ ] **Event System**: Supabase Realtime for event streaming and notifications
- [ ] **Monitoring**: Sentry error tracking, PostHog analytics, and OpenReplay session replay

### AI-Powered Capabilities

#### Natural Language Processing
- [ ] **Query Understanding**: Advanced natural language processing via Agno 1.2.2 framework
- [ ] **Conversation Management**: Multi-agent context-aware conversations with 23+ model providers
- [ ] **Language Support**: English and French processing with 95% accuracy via GPT-4, Claude, Gemini
- [ ] **Intent Recognition**: Smart interpretation of user analysis requests using OpenAI/OpenRouter APIs

#### Statistical Analysis Engine
- [ ] **Advanced Analytics**: Statistical analysis using pandas 2.2.3, NumPy 2.2.0, and scipy
- [ ] **Pattern Recognition**: Automated outlier detection using scikit-learn and AI models
- [ ] **Predictive Modeling**: Regression analysis and time series forecasting via Python libraries
- [ ] **Descriptive Statistics**: Comprehensive statistical summaries powered by pandas and NumPy

#### Computer Vision & OCR
- [ ] **Document Processing**: Multi-modal AI models (GPT-4 Vision, Claude 3 Vision, Gemini Vision) for OCR and table extraction
- [ ] **Receipt Scanning**: Invoice and receipt processing via vision models integrated through Agno framework
- [ ] **Batch Processing**: Automated processing of multiple files using AI vision capabilities
- [ ] **Format Recognition**: Smart detection of data structures using multi-modal AI models

#### Data Visualization
- [ ] **Smart Charts**: Chart generation using matplotlib 3.10.0 and plotly 6.1.2 with AI recommendations
- [ ] **Interactive Dashboards**: Dynamic analytics dashboards with Shadcn/ui components and Magic UI animations
- [ ] **Excel Integration**: Direct embedding of visualizations in Excel sheets via Office.js APIs
- [ ] **African Templates**: Custom visualization templates for regional business needs
- [ ] **Export Options**: Professional presentation-ready export capabilities with plotly integration

#### Data Processing & Cleaning
- [ ] **Automated Workflows**: Intelligent data cleaning pipelines using pandas and AI-guided processes
- [ ] **Quality Control**: Missing value handling and duplicate detection via pandas and NumPy
- [ ] **Standardization**: Format standardization and data validation using Python libraries
- [ ] **Guided Assistance**: Voice and chat-guided data cleaning via Agno multi-agent system
- [ ] **Batch Operations**: Large-scale data processing using hybrid Pyodide/E2B sandbox execution

### Excel Integration & Data Access

#### Native Excel Integration
- [ ] **Cross-Platform Support**: Native sidebar add-in for Excel Desktop, Mac, and Online
- [ ] **Bidirectional Data Flow**: Write AI outputs directly to selected cells and read from ranges
- [ ] **Custom Functions**: Advanced Excel functions for specialized operations
- [ ] **Real-time Sync**: Live updates between add-in and Excel data

#### Data Source Connectivity (Hybrid Architecture)
- [ ] **Phase 1 (MVP) - Traditional Database Drivers**:
  - [ ] **Local Files**: Direct access to Excel, CSV, and PDF files on local system
  - [ ] **Cloud Integration**: OneDrive/Office 365 seamless integration
  - [ ] **Database Connections**: PostgreSQL, MySQL, Microsoft SQL Server, Oracle, Snowflake, and Google Sheets integration
  - [ ] **Business Systems**: Salesforce CRM, Zoho CRM, and QuickBooks Online API integration
  - [ ] **Performance Optimization**: Optimized for African market connectivity challenges
- [ ] **Phase 2 (Enhancement) - Advanced AI Integration**:
  - [ ] **Model Context Protocol (MCP)**: Natural language database queries via MCP servers
  - [ ] **AG-UI Protocol**: Real-time agent-user interaction with streaming responses
  - [ ] **Cross-Source Analytics**: Multi-database AI analysis and insights
  - [ ] **Dynamic Schema Discovery**: AI-powered database exploration and documentation
  - [ ] **Interactive AI Workflows**: Human-in-the-loop collaboration and guidance
  - [ ] **Intelligent Routing**: Automatic selection between direct and MCP access
- [ ] **Web Data Extraction**: URL-based tabular data extraction and web research
- [ ] **API Integrations**: RESTful API connections for external data sources
- [ ] **AG-UI Protocol Integration**: Real-time agent-user interaction with streaming responses and enhanced UI feedback for seamless human-AI collaboration

### Business Model & Security

#### Wallet-Based Pay-Per-Use Model (Revolutionary Pricing Strategy - June 2025)
- [ ] **Minimum Wallet Load**: $5 for all users (individual and enterprise) - universal accessibility
- [ ] **Cost Per Query**: $0.02-0.20 based on complexity (Simple: $0.02-0.04, Code: $0.06-0.09, Complex: $0.12-0.20, Average: $0.09)
- [ ] **AI Model Pricing**: Gemini 2.5 Pro ($1.25/$10.00 per 1K tokens), DeepSeek Coder ($0.27/$1.10), DeepSeek R1 ($0.55/$2.19), Gemini Flash ($0.15/$0.60)
- [ ] **Gross Margin**: 20-33% markup on all queries ensuring profitability (32% average)
- [ ] **Optional Transparency**: Users can toggle cost visibility and detailed token breakdowns on/off
- [ ] **Auto-Refill**: Automatic wallet replenishment when balance drops below $2.00 threshold

#### Custom Cost Calculator & Markup Engine (Core Business Logic)
- [ ] **Custom Implementation**: Build proprietary cost calculation system (no external pricing libraries)
- [ ] **Comprehensive Cost Accounting**: Include all production costs - AI models, infrastructure, operations, payment processing
- [ ] **Real-Time Calculation**: Frontend pre-query estimation + backend actual cost tracking
- [ ] **Financial Precision**: Use decimal.js for all monetary calculations to prevent floating-point errors
- [ ] **Dynamic Markup Logic**: Intelligent pricing based on query complexity, model selection, and operational costs
- [ ] **Profit Margin Management**: Ensure 32% gross margin through automated markup calculations
- [ ] **Cost Optimization**: Route queries to most cost-effective models while maintaining quality
- [ ] **Production Cost Structure**:
  - AI model costs (base provider rates)
  - Infrastructure: Supabase ($0.0001), Vercel ($0.0002), Cloudflare ($0.00005) per query
  - Operations: Support ($0.002), Development ($0.003), Marketing ($0.001) per query allocation
  - Payment processing: 1.95-2.9% based on provider (Paystack/Flutterwave/Stripe)
- [ ] **Business Intelligence**: Cost analytics, margin tracking, and pricing optimization insights
- [ ] **Enterprise Features**: Team wallets, admin controls, shared budgets, usage analytics (typical loads: $200-1,000 based on usage)
- [ ] **Regional Payment**: Ghana (GHS), Nigeria (NGN) with local payment methods via Paystack/Flutterwave

#### Security & Compliance Framework
- [ ] **Data Protection**: GDPR and African data protection law compliance
- [ ] **Execution Security**: Sandboxed Python environments with strict isolation
- [ ] **Access Control**: Role-based access control (RBAC) and user data isolation
- [ ] **Encryption**: End-to-end encryption with automatic data erasure options
- [ ] **Monitoring**: Comprehensive usage monitoring and analytics

## Design & User Experience Requirements

### Design System Foundation
- **Style Guide**: Clean, modern Apple-inspired design using Midday.ai as primary style reference
- **Design Direction**: Subdued professional palette with minimalist patterns prioritizing usability
- **Header Design**: Clean header without 'Excella' text/branding for minimal visual clutter
- **Branding**: Use 'Excella' (without 'AI' suffix) in message displays for cleaner branding

### Excel Add-in Interface (React 19 + Modern Component System)
- [x] **Modern Chat UI**: Clean black/white conversational interface with personalized greeting and Excella avatar
- [x] **Agent/Chat Mode Toggle**: Smart mode switching between Agent and Chat interactions in input area
- [x] **Voice Integration**: Microphone icon integrated directly in text input field for seamless voice input
- [x] **Four Suggested Actions**: Exactly 4 action cards with colored icons (Ask question, Draft anything, Brainstorm ideas, What can Excel AI do?)
- [x] **Server Connection Status**: Server icon with color-coded status (green blink = connected, red = disconnected)
- [x] **Fluxitude Branding**: "Powered by Fluxitude" footer establishing parent company relationship
- [ ] **Animated Feedback**: Magic UI components for loading states, progress indicators, and visual feedback
- [ ] **Multilingual Support**: Bilingual UI (English/French) with next-intl and smooth transition animations
- [ ] **Smart Interactions**: Cell reference highlighting with animated focus states and context-aware suggestions
- [ ] **Accessibility**: High contrast themes, reduced motion preferences, and full accessibility compliance
- [ ] **Responsive Design**: Optimized for different Excel window sizes with fluid animations and Tailwind CSS
- [ ] **Micro-interactions**: Hover effects, button animations, and smooth state transitions via Magic UI

### Web Application Interface (Next.js 15 + Modern Stack)
- [ ] **Component Library**: Built with React 19.0.0, Shadcn/ui, and Tailwind CSS 3.4.x for consistent design
- [ ] **Animated Components**: Magic UI integration for enhanced visual feedback, loading states, and micro-interactions
- [ ] **Theme System**: Dark/light mode with system preference detection using Tailwind CSS
- [ ] **Interactive Features**: Dynamic visualization editor and dashboard components with Magic UI animations
- [ ] **Internationalization**: Complete bilingual interface with next-intl for Next.js 15 App Router
- [ ] **Analytics Dashboard**: PostHog-powered analytics and reporting interface with animated charts
- [ ] **Documentation Platform**: Mintlify-powered docs with AI-powered search capabilities
- [ ] **Regional Adaptation**: African market-specific templates and components
- [ ] **Progressive Web App**: PWA capabilities with Workbox 7.x for enhanced desktop experience
- [ ] **Visual Feedback**: Magic UI animated loading states, success/error notifications, and progress indicators

## Technical Implementation Requirements

### Development Infrastructure & DevOps
- [ ] **Deployment Pipeline**: Vercel-based deployment optimized for Next.js 15 with automated CI/CD via GitHub Actions
- [ ] **Testing Framework**: Vitest 3.1.4, React Testing Library 16.3.0, and Playwright 1.52.0 for comprehensive testing
- [ ] **Code Quality**: TypeScript 5.6.0 strict mode, ESLint 9.27.0, Prettier 3.5.3, and Husky 9.x pre-commit hooks
- [ ] **Documentation**: Storybook 9.0.3 for components, Mintlify for API docs with OpenAPI integration
- [ ] **Monitoring Stack**: PostHog analytics, OpenReplay 16.2.1 session replay, and Sentry 9.24.0 error tracking

### Performance Requirements

#### Excel Add-in Optimization
- [ ] **Bundle Efficiency**: Optimized bundle size for React 19.0.0, Shadcn/ui, and Magic UI components
- [ ] **State Management**: Efficient Zustand 5.0.5-based state management with minimal re-renders
- [ ] **Resource Management**: Lazy loading, memory management, and client-side caching with Pyodide
- [ ] **Responsiveness**: Sub-2-second response times for all user interactions and AI processing

#### Web Application Performance
- [ ] **Modern Architecture**: Next.js 15 App Router with React 19 server components and edge runtime
- [ ] **Database Optimization**: Supabase PostgreSQL 15.x connection pooling, caching, and optimized queries
- [ ] **Asset Delivery**: Cloudflare CDN optimization and Next/Image for efficient asset delivery
- [ ] **Regional Performance**: Supabase Edge Functions deployment for African market optimization
- [ ] **Real-time Features**: Supabase Realtime performance tuning for live updates and data synchronization

### System Integration & Security

#### Platform Integration
- [ ] **Cross-Platform Auth**: Secure Supabase Auth with @supabase/ssr across Excel add-in and web app
- [ ] **Real-time Sync**: Supabase Realtime for live data synchronization and updates
- [ ] **Offline Support**: Pyodide WebAssembly for offline Python execution and local storage capabilities
- [ ] **Serverless Compute**: Supabase Edge Functions with Deno 2.x for scalable backend processing
- [ ] **File Management**: Supabase Storage integration for document and data handling
- [ ] **API Design**: tRPC 11.2.0 procedures for type-safe API communication with React 19 compatibility

#### Security Implementation
- [ ] **Transport Security**: HTTPS everywhere with proper SSL/TLS configuration
- [ ] **Database Security**: Supabase Row Level Security (RLS) and PostgreSQL 15.x access policies
- [ ] **Authentication**: OAuth providers integration with Supabase Auth and @supabase/ssr
- [ ] **Rate Protection**: Upstash-based rate limiting and DDoS protection via Cloudflare
- [ ] **Input Validation**: Comprehensive input sanitization and CORS configuration
- [ ] **Content Security**: Content Security Policy (CSP) implementation for React 19 apps
- [ ] **Sandbox Security**: Pyodide WebAssembly isolation and E2B Code Interpreter security
- [ ] **Data Protection**: End-to-end encryption at rest and in transit
- [ ] **Backup Strategy**: Automated Supabase backup and disaster recovery procedures

### Monitoring & Analytics Framework
- [ ] **Product Analytics**: PostHog integration with feature flags and A/B testing
- [ ] **User Behavior**: Session recording, heatmaps, and custom event tracking
- [ ] **Performance Monitoring**: OpenReplay for real user monitoring and performance insights
- [ ] **Error Management**: Sentry integration for comprehensive error tracking and alerting
- [ ] **User Feedback**: Integrated survey tools and retention analysis
- [ ] **Regional Metrics**: Custom dashboards for African market-specific analytics

### Regional Optimization Strategy
- [ ] **Infrastructure**: Edge function deployment optimized for African regions
- [ ] **Performance**: Bandwidth optimization and regional database instances
- [ ] **Content Delivery**: CDN optimization for African internet infrastructure
- [ ] **Localization**: Comprehensive localization management for English/French markets
- [ ] **Compliance**: Regional compliance monitoring for African data protection laws

### Quality Assurance & Testing
- [ ] **Unit Testing**: Comprehensive Vitest-based unit and integration testing
- [ ] **Component Testing**: React Testing Library for UI component validation
- [ ] **End-to-End Testing**: Playwright for complete user journey testing
- [ ] **API Testing**: Automated API testing with Vitest and tRPC validation
- [ ] **Performance Testing**: Lighthouse integration for Core Web Vitals monitoring
- [ ] **Visual Testing**: Visual regression testing and Storybook integration
- [ ] **Continuous Testing**: Automated testing pipeline in CI/CD with coverage reporting

### Documentation & Knowledge Management
- [ ] **Platform**: Mintlify-powered documentation with OpenAPI/Swagger integration
- [ ] **Interactive Features**: API playground and interactive code examples
- [ ] **Multilingual**: Complete bilingual documentation (English/French)
- [ ] **Versioning**: Automated versioning for API changes and SDK documentation
- [ ] **AI Integration**: AI-powered documentation chat and search capabilities
- [ ] **Community**: Community contribution workflow and usage analytics
- [ ] **Regional Content**: Africa-specific documentation sections and integration guides
- [ ] **Media Integration**: Video documentation and automated changelog generation

## Technology Stack Summary

### Frontend Technologies
- **Excel Add-in**: React 19.0.0 with TypeScript 5.6.0, Shadcn/ui components, Magic UI animations, Office.js ExcelApi 1.17+
- **Web Application**: Next.js 15.x (App Router), Tailwind CSS 3.4.x, Shadcn/ui + Magic UI components, Framer Motion, Vercel deployment

### Backend Infrastructure
- **Database**: Supabase PostgreSQL 15.x with Row Level Security and real-time capabilities
- **Authentication**: Supabase Auth with @supabase/ssr and OAuth provider integration
- **Real-time**: Supabase Realtime for live data synchronization and updates
- **Storage**: Supabase Storage for file and document management
- **API**: tRPC 11.2.0 with Supabase Edge Functions (Deno 2.x) for type-safe communication

### AI & Data Processing (Cost-Optimized December 2024) - ENHANCED
- **AI Framework**: Agno 1.2.2 with intelligent orchestrator model architecture for maximum cost efficiency
- **Orchestrator Architecture**: Gemini 2.5 Pro as main orchestrator (2M token context window) that intelligently delegates to specialist models
- **Specialist Models**: DeepSeek Coder (128K context), Gemini Flash (1M context), DeepSeek R1-0528 (64K context) - context window sizes considered for optimal task routing
- **AI Model Costs**: Gemini 2.5 Pro ($1.25/$10.00 per 1K tokens), DeepSeek Coder ($0.27/$1.10), DeepSeek R1 ($0.55/$2.19), Gemini Flash ($0.15/$0.60)
- **Cost Per Query**: $0.068 average cost, $0.09 average revenue (32% gross margin through smart routing)
- **Excel Integration**: Gemini 2.5 Pro provides native spreadsheet support and superior mathematical capabilities
- **Sandbox Strategy**: Hybrid Pyodide (WebAssembly) + E2B Code Interpreter execution
- **Data Libraries**: pandas 2.2.3, NumPy 2.2.0, scipy, scikit-learn, matplotlib 3.10.0, plotly 6.1.2
- **Visualization Libraries**: react-plotly.js, @tanstack/react-table, prism-react-renderer, exceljs, file-saver
- **NLP Processing**: spaCy 3.8.6, transformers, OpenAI Whisper API for voice processing

### Development & Quality Tools
- **Testing**: Vitest 3.1.4, React Testing Library 16.3.0, Playwright 1.52.0, Storybook 9.0.3
- **Code Quality**: ESLint 9.27.0, TypeScript ESLint 8.x, Prettier 3.5.3, Husky 9.x
- **State Management**: Zustand 5.0.5 for lightweight, React 19-compatible state management

### Monitoring & Analytics
- **Product Analytics**: PostHog for user behavior, feature flags, and A/B testing
- **Error Tracking**: Sentry 9.24.0 for comprehensive error monitoring and performance tracking
- **Session Replay**: OpenReplay 16.2.1 for user experience insights and debugging
- **Rate Limiting**: Upstash for Redis-based rate limiting and caching

## Financial Model & Unit Economics (June 2025 Validation)

### Strategic Pivot: Subscription to Wallet-Based Model

**Previous Subscription Model (FAILED):**
- Professional Tier: $20/month revenue, $75.46 cost = **$55.46 LOSS per user**
- Team Tier: $18/month revenue, $95.89 cost = **$77.89 LOSS per user**
- Break-even: Impossible at any reasonable pricing level
- Market Position: Unsustainable, required 275-433% price increases

**New Wallet-Based Model (PROFITABLE):**
- Revenue per Query: $0.09 average
- Cost per Query: $0.068 average
- Gross Margin: 32% per query
- Break-even: 67 active users (vs. impossible under subscriptions)

### Validated Wallet-Based Pay-Per-Use Strategy
- **Minimum Wallet Load**: $5 for all users (individual and enterprise) - universal accessibility
- **Cost Per Query**: $0.02-0.20 based on complexity (Simple: $0.02-0.04, Advanced: $0.12-0.20, Average: $0.09)
- **Gross Margin**: 20-33% markup on all queries ensuring profitability (32% average per financial analysis)
- **Average User Spend**: $7.50-15.00/month (Light to Power users)
- **Enterprise Spend**: $200-1,000/month for teams (1,500-8,000 queries) - based on usage, not minimum requirements
- **Auto-Refill**: Automatic wallet replenishment when balance drops below $2.00
- **Break-even**: Month 1-2 with 67+ active users (validated financial analysis)
- **Target ARR**: $90K-1.8M with 1,000-10,000 active users (Conservative: $90K, Realistic: $675K, Optimistic: $1.8M)

> **⚠️ CRITICAL USAGE PATTERN WARNING**: Internal research indicates actual usage patterns may be 2-3x higher than conservative projections, with 500-800 queries/month average and $0.08-0.15 per query costs. Financial projections use conservative estimates that may require significant revision based on real-world usage data.

### Orchestrator Model Architecture Strategy
- **Cost-Efficiency Focus**: Intelligent orchestrator architecture balancing performance with cost efficiency for African markets
- **Orchestrator Model**: Gemini 2.5 Pro (2M token context) as main orchestrator that intelligently delegates tasks to specialist models
- **Specialist Models**: DeepSeek Coder (128K context, coding tasks), Gemini Flash (1M context, cost-efficient operations), DeepSeek R1-0528 (64K context, advanced reasoning)
- **Context Window Strategy**: Large context windows prioritized for complex Excel analysis and multi-turn conversations
- **Intelligent Routing**: System automatically selects optimal models based on query complexity, context requirements, and type - users don't choose models directly
- **Operational Costs**: $9.5K-17.9K annually (infrastructure $600, AI APIs $8,400-16,800, tools $500) - 30% cost reduction
- **Payment Processing**: 1.95-2.0% (Ghana/Nigeria local), 4.8% (international via Flutterwave)

### Market Validation & Competitive Analysis
- **Ghana Market**: Wallet-based model accessible (1-3% of income vs. 6-10% for subscriptions), $5-10 loads match budget constraints
- **Nigeria Market**: Pay-per-use removes subscription barriers (3-12% of income), $5-15 loads accessible to most professionals
- **Competitive Position**: Unique pay-per-use model vs. Julius AI ($20-45/month), Microsoft Copilot ($20-30/month), ChatGPT Plus ($20/month)
- **Market Differentiation**: Only Excel AI tool with transparent, developer-grade pricing and no subscription barriers
- **Usage-Based Pricing Validation**: 78% of companies adopted usage-based pricing in last 5 years (Metronome 2025), 64% of Forbes Next Billion-Dollar Startups use usage-based pricing
- **Unit Economics**: 32% gross margin per query vs. current subscription model losses of $55-77/user/month

## African Market Considerations

### Regional Requirements (Updated December 2024)
- [ ] **Compliance**: Ghana Data Protection Act and Nigeria NDPR with basic GDPR alignment
- [ ] **Payment Processing**: Primary: Paystack Ghana (1.95% fees), Flutterwave Nigeria (2.0% local, 4.8% international); Backup: Stripe, PayPal for global reliability
- [ ] **Infrastructure**: Cloudflare CDN with African POPs for bandwidth optimization
- [ ] **Localization**: English/French language support with next-intl and cultural adaptations
- [ ] **Pricing**: Regional currency display (GHS, NGN) with USD billing
- [ ] **Templates**: Africa-specific business use case templates and examples
- [ ] **Support**: Community-driven support and WhatsApp-based user engagement
- [ ] **Offline Capabilities**: Pyodide WebAssembly for offline data processing in low-connectivity areas

## WhatsApp Community Strategy

### Community Platform Choice Rationale
WhatsApp is the optimal community platform for Excella's African market focus, offering superior engagement and accessibility compared to Discord or other platforms.

### WhatsApp Advantages for African Markets
- [ ] **Market Penetration**: 90%+ adoption rate in Ghana/Nigeria vs. <20% Discord usage
- [ ] **Data Efficiency**: Works on 2G/3G networks with minimal data consumption
- [ ] **Business Integration**: Already used by African businesses for customer service
- [ ] **Voice Support**: Native voice messaging supports multilingual community (English/French)
- [ ] **Document Sharing**: Easy Excel file sharing for troubleshooting and examples
- [ ] **Zero Learning Curve**: Familiar interface requires no user training

### Community Structure & Groups
- [ ] **Main Community**: General discussions, announcements, feature updates (100-200 members)
- [ ] **Technical Support**: Troubleshooting, Excel integration help, AI query assistance (50-100 members)
- [ ] **Tips & Tutorials**: Weekly Excel tips, AI best practices, business case studies (150-300 members)
- [ ] **Feedback & Beta**: Product feedback, feature requests, beta testing coordination (20-50 members)
- [ ] **Affiliate Partners**: Dedicated group for affiliate program coordination and training (10-25 members)

### Engagement Strategy
- [ ] **Daily Tips**: Excel + AI productivity tips via broadcast messages
- [ ] **Weekly Tutorials**: Voice messages explaining new features and use cases
- [ ] **Monthly Q&A**: Live voice chat sessions for user questions and feedback
- [ ] **Success Stories**: User case studies and testimonials shared in community
- [ ] **Direct Support**: Personal troubleshooting via voice messages and screen sharing

### Moderation & Management
- [ ] **Community Guidelines**: Clear rules for professional, helpful discussions
- [ ] **Automated Responses**: WhatsApp Business API for common questions and onboarding
- [ ] **Escalation Process**: Technical issues escalated from community to direct support
- [ ] **Content Curation**: Regular sharing of valuable Excel templates and business insights
- [ ] **Language Support**: Bilingual moderation for English and French speakers