<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excella Web Dashboard Mockup</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#111827',
                        accent: '#0ea5e9',
                        brand: { primary: '#111827', secondary: '#64748b', accent: '#0ea5e9' }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'counter': 'counter 2s ease-out'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        @keyframes counter {
            from { transform: scale(0.8); }
            to { transform: scale(1); }
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .gradient-border {
            background: linear-gradient(135deg, #f97316, #10b981);
            padding: 1px;
            border-radius: 12px;
        }
        
        .gradient-border-inner {
            background: white;
            border-radius: 11px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-orange-50/30 min-h-screen">
    <!-- Navigation Header -->
    <header class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 px-6 py-4 sticky top-0 z-50">
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center gap-6">
                <!-- Brand -->
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-accent flex items-center justify-center shadow-lg">
                        <span class="text-white font-bold text-lg">E</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Excella</h1>
                        <p class="text-xs text-gray-500">AI-Powered Analytics</p>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex items-center gap-1">
                    <a href="#" class="px-4 py-2 bg-primary/10 text-primary rounded-lg font-medium text-sm">Dashboard</a>
                    <a href="#" class="px-4 py-2 hover:bg-gray-100 rounded-lg text-gray-600 text-sm">Analytics</a>
                    <a href="#" class="px-4 py-2 hover:bg-gray-100 rounded-lg text-gray-600 text-sm">Team</a>
                    <a href="#" class="px-4 py-2 hover:bg-gray-100 rounded-lg text-gray-600 text-sm">Billing</a>
                </nav>
            </div>
            
            <div class="flex items-center gap-4">
                <!-- Notifications -->
                <button class="relative p-2 hover:bg-gray-100 rounded-lg transition-colors">
                    <span class="text-xl">🔔</span>
                    <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">3</span>
                </button>
                
                <!-- User Menu -->
                <div class="flex items-center gap-3">
                    <div class="text-right hidden sm:block">
                        <p class="text-sm font-medium text-gray-900">Kwame Asante</p>
                        <p class="text-xs text-gray-500">Wallet: $12.50</p>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-ghana-green to-ghana-gold flex items-center justify-center text-white font-semibold shadow-lg">
                        KA
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex max-w-7xl mx-auto">
        <!-- Sidebar -->
        <nav class="w-64 bg-white/60 backdrop-blur-md border-r border-gray-200/50 min-h-screen p-6 hidden lg:block">
            <div class="space-y-2">
                <div class="mb-6">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Main</h3>
                    <div class="space-y-1">
                        <a href="#" class="flex items-center gap-3 p-3 bg-gradient-to-r from-primary/10 to-accent/10 text-primary rounded-xl font-medium">
                            <span class="text-lg">📊</span>
                            <span>Dashboard</span>
                            <span class="ml-auto bg-primary/20 text-primary text-xs px-2 py-1 rounded-full">New</span>
                        </a>
                        <a href="#" class="flex items-center gap-3 p-3 hover:bg-gray-100/50 rounded-xl text-gray-700 transition-colors">
                            <span class="text-lg">📈</span>
                            <span>Analytics</span>
                        </a>
                        <a href="#" class="flex items-center gap-3 p-3 hover:bg-gray-100/50 rounded-xl text-gray-700 transition-colors">
                            <span class="text-lg">👥</span>
                            <span>Team</span>
                            <span class="ml-auto bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">5</span>
                        </a>
                        <a href="#" class="flex items-center gap-3 p-3 hover:bg-gray-100/50 rounded-xl text-gray-700 transition-colors">
                            <span class="text-lg">💳</span>
                            <span>Billing</span>
                        </a>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Tools</h3>
                    <div class="space-y-1">
                        <a href="#" class="flex items-center gap-3 p-3 hover:bg-gray-100/50 rounded-xl text-gray-700 transition-colors">
                            <span class="text-lg">📋</span>
                            <span>Reports</span>
                        </a>
                        <a href="#" class="flex items-center gap-3 p-3 hover:bg-gray-100/50 rounded-xl text-gray-700 transition-colors">
                            <span class="text-lg">⚙️</span>
                            <span>Settings</span>
                        </a>
                        <a href="#" class="flex items-center gap-3 p-3 hover:bg-gray-100/50 rounded-xl text-gray-700 transition-colors">
                            <span class="text-lg">❓</span>
                            <span>Help</span>
                        </a>
                    </div>
                </div>
                
                <!-- Upgrade Card -->
                <div class="gradient-border">
                    <div class="gradient-border-inner p-4">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-3">
                                <span class="text-white text-xl">🚀</span>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-1">Upgrade to Team</h4>
                            <p class="text-xs text-gray-600 mb-3">Get advanced analytics and team collaboration</p>
                            <button class="w-full bg-gradient-to-r from-primary to-accent text-white py-2 rounded-lg text-sm font-medium hover:shadow-lg transition-all">
                                Upgrade Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 p-6 lg:p-8">
            <!-- Welcome Section -->
            <div class="mb-8 animate-fade-in">
                <div class="flex items-center justify-between mb-2">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome back, Kwame! 👋</h1>
                        <p class="text-gray-600">Here's what's happening with your data analysis today</p>
                    </div>
                    <button class="bg-gradient-to-r from-primary to-accent text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all">
                        📅 View Reports
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Queries Used -->
                <div class="bg-white/80 backdrop-blur-md p-6 rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all animate-slide-up">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-600 font-medium">Queries Used</span>
                        <div class="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                            <span class="text-primary text-lg">⚡</span>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-gray-900 mb-2 animate-counter">25<span class="text-lg text-gray-500">/100</span></div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-primary to-orange-600 h-2 rounded-full transition-all duration-1000" style="width: 25%"></div>
                    </div>
                    <p class="text-xs text-gray-500">25% of monthly limit</p>
                </div>

                <!-- Team Members -->
                <div class="bg-white/80 backdrop-blur-md p-6 rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all animate-slide-up" style="animation-delay: 0.1s">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-600 font-medium">Team Members</span>
                        <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                            <span class="text-blue-600 text-lg">👥</span>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-gray-900 mb-2 animate-counter">5</div>
                    <p class="text-xs text-gray-500">Active team members</p>
                </div>

                <!-- Monthly Spend -->
                <div class="bg-white/80 backdrop-blur-md p-6 rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all animate-slide-up" style="animation-delay: 0.2s">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-600 font-medium">Monthly Spend</span>
                        <div class="w-10 h-10 bg-ghana-green/10 rounded-xl flex items-center justify-center">
                            <span class="text-ghana-green text-lg">💰</span>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-gray-900 mb-2 animate-counter">₵450</div>
                    <p class="text-xs text-gray-500">Current billing period</p>
                </div>

                <!-- Growth -->
                <div class="bg-white/80 backdrop-blur-md p-6 rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all animate-slide-up" style="animation-delay: 0.3s">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-600 font-medium">Growth</span>
                        <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                            <span class="text-green-600 text-lg">📈</span>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-green-600 mb-2 animate-counter flex items-center">
                        +12%
                        <span class="text-sm ml-2">↗️</span>
                    </div>
                    <p class="text-xs text-gray-500">vs. last month</p>
                </div>
            </div>

            <!-- Recent Activity & Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Recent Activity -->
                <div class="lg:col-span-2 bg-white/80 backdrop-blur-md rounded-2xl border border-gray-200/50 shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                        <button class="text-sm text-primary hover:text-primary/80 font-medium">View All</button>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center gap-4 p-4 bg-green-50 rounded-xl border border-green-200">
                            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">✓</span>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Sales data analysis completed</p>
                                <p class="text-xs text-gray-500">Generated insights for Q4 performance</p>
                            </div>
                            <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">2 min ago</span>
                        </div>
                        
                        <div class="flex items-center gap-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">👤</span>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">New team member added</p>
                                <p class="text-xs text-gray-500">Ama Osei joined the analytics team</p>
                            </div>
                            <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">1 hour ago</span>
                        </div>
                        
                        <div class="flex items-center gap-4 p-4 bg-orange-50 rounded-xl border border-orange-200">
                            <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">📊</span>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Monthly report generated</p>
                                <p class="text-xs text-gray-500">December analytics summary ready</p>
                            </div>
                            <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">3 hours ago</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white/80 backdrop-blur-md rounded-2xl border border-gray-200/50 shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
                    <div class="space-y-3">
                        <button class="w-full p-4 bg-gradient-to-r from-primary/10 to-accent/10 hover:from-primary/20 hover:to-accent/20 rounded-xl text-left transition-all group">
                            <div class="flex items-center gap-3">
                                <span class="text-2xl group-hover:scale-110 transition-transform">🚀</span>
                                <div>
                                    <p class="font-medium text-gray-900">Connect Excel</p>
                                    <p class="text-xs text-gray-600">Link your Excel workbooks</p>
                                </div>
                            </div>
                        </button>
                        
                        <button class="w-full p-4 bg-gray-50 hover:bg-gray-100 rounded-xl text-left transition-all group">
                            <div class="flex items-center gap-3">
                                <span class="text-2xl group-hover:scale-110 transition-transform">📈</span>
                                <div>
                                    <p class="font-medium text-gray-900">Create Dashboard</p>
                                    <p class="text-xs text-gray-600">Build custom analytics</p>
                                </div>
                            </div>
                        </button>
                        
                        <button class="w-full p-4 bg-gray-50 hover:bg-gray-100 rounded-xl text-left transition-all group">
                            <div class="flex items-center gap-3">
                                <span class="text-2xl group-hover:scale-110 transition-transform">👥</span>
                                <div>
                                    <p class="font-medium text-gray-900">Invite Team</p>
                                    <p class="text-xs text-gray-600">Add team members</p>
                                </div>
                            </div>
                        </button>
                        
                        <button class="w-full p-4 bg-gray-50 hover:bg-gray-100 rounded-xl text-left transition-all group">
                            <div class="flex items-center gap-3">
                                <span class="text-2xl group-hover:scale-110 transition-transform">💬</span>
                                <div>
                                    <p class="font-medium text-gray-900">WhatsApp Support</p>
                                    <p class="text-xs text-gray-600">Get help instantly</p>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Navigation (hidden on desktop) -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-md border-t border-gray-200 p-4 lg:hidden">
        <div class="flex items-center justify-around">
            <button class="flex flex-col items-center gap-1 text-primary">
                <span class="text-lg">📊</span>
                <span class="text-xs">Dashboard</span>
            </button>
            <button class="flex flex-col items-center gap-1 text-gray-500">
                <span class="text-lg">📈</span>
                <span class="text-xs">Analytics</span>
            </button>
            <button class="flex flex-col items-center gap-1 text-gray-500">
                <span class="text-lg">💬</span>
                <span class="text-xs">AI Chat</span>
            </button>
            <button class="flex flex-col items-center gap-1 text-gray-500">
                <span class="text-lg">👤</span>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>
</body>
</html>
