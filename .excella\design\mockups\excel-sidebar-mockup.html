<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excella Excel Add-in Mockup</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f97316',
                        accent: '#10b981',
                        ghana: { red: '#CE1126', gold: '#FCD116', green: '#006B3F' },
                        nigeria: { green: '#008751' }
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(249, 115, 22, 0.3);
            transform: scale(0);
            animation: ripple-animation 2s infinite;
        }
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        .typing-indicator {
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }
        .typing-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: #f97316;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .server-icon {
            filter: hue-rotate(120deg) brightness(1.2);
            transition: all 0.3s ease;
        }

        .server-icon.disconnected {
            filter: hue-rotate(0deg) brightness(0.8);
            animation: none;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Excel Sidebar Container (360px width) -->
    <div class="w-[360px] h-screen bg-white shadow-2xl flex flex-col mx-auto">
        <!-- Header -->
        <header class="flex items-center justify-between p-3 border-b bg-white/95 backdrop-blur">
            <div class="flex items-center gap-2">
                <span class="text-lg server-icon animate-pulse" title="Connected to AI service">🖥️</span>
            </div>
            <div class="flex items-center gap-1">
                <button class="p-1.5 hover:bg-gray-100 rounded-md transition-colors" title="Add new">
                    <span class="text-sm">➕</span>
                </button>
                <button class="p-1.5 hover:bg-gray-100 rounded-md transition-colors" title="History">
                    <span class="text-sm">⟲</span>
                </button>
                <button class="p-1.5 hover:bg-gray-100 rounded-md transition-colors" title="Settings">
                    <span class="text-sm">⚙️</span>
                </button>
                <button class="p-1.5 hover:bg-gray-100 rounded-md transition-colors" title="Close">
                    <span class="text-sm">✕</span>
                </button>
            </div>
        </header>

        <!-- Quick Actions -->
        <div class="p-3 border-b bg-gradient-to-r from-gray-50 to-orange-50/30">
            <div class="flex items-center gap-2 mb-2">
                <span class="text-primary animate-pulse-slow">✨</span>
                <span class="text-xs font-medium text-gray-700">Quick Actions</span>
            </div>
            <div class="grid grid-cols-2 gap-2">
                <button class="p-2 border border-gray-200 rounded-lg hover:bg-white hover:shadow-sm transition-all text-xs flex flex-col items-center gap-1 group">
                    <span class="text-base group-hover:scale-110 transition-transform">📊</span>
                    <span class="text-gray-700">Summarize</span>
                </button>
                <button class="p-2 border border-gray-200 rounded-lg hover:bg-white hover:shadow-sm transition-all text-xs flex flex-col items-center gap-1 group">
                    <span class="text-base group-hover:scale-110 transition-transform">📈</span>
                    <span class="text-gray-700">Create Chart</span>
                </button>
                <button class="p-2 border border-gray-200 rounded-lg hover:bg-white hover:shadow-sm transition-all text-xs flex flex-col items-center gap-1 group">
                    <span class="text-base group-hover:scale-110 transition-transform">🔍</span>
                    <span class="text-gray-700">Find Trends</span>
                </button>
                <button class="p-2 border border-gray-200 rounded-lg hover:bg-white hover:shadow-sm transition-all text-xs flex flex-col items-center gap-1 group">
                    <span class="text-base group-hover:scale-110 transition-transform">🧮</span>
                    <span class="text-gray-700">Calculate</span>
                </button>
            </div>
        </div>

        <!-- Conversation Area -->
        <div class="flex-1 p-3 overflow-y-auto space-y-4 bg-gradient-to-b from-white to-gray-50/50">
            <!-- Selected cells indicator -->
            <div class="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-lg text-xs">
                <span class="text-blue-600">📋</span>
                <span class="text-blue-700">Selected: A1:A20 (Sales Data)</span>
            </div>

            <!-- User Message -->
            <div class="flex justify-end">
                <div class="bg-primary text-white p-3 rounded-2xl rounded-br-md max-w-[85%] text-sm shadow-sm">
                    Analyze the sales data in column A and show me the trends
                </div>
            </div>
            
            <!-- AI Response with Analysis -->
            <div class="flex gap-2">
                <div class="w-7 h-7 rounded-full bg-gradient-to-br from-accent to-emerald-600 flex items-center justify-center text-white text-xs font-semibold shadow-sm">
                    E
                </div>
                <div class="bg-white border border-gray-200 p-4 rounded-2xl rounded-bl-md flex-1 text-sm shadow-sm">
                    <p class="mb-3 text-gray-800">I've analyzed your sales data from A1:A20. Here are the key insights:</p>
                    
                    <!-- Statistics Card -->
                    <div class="bg-gradient-to-r from-gray-50 to-blue-50 p-3 rounded-lg border mb-3">
                        <div class="text-xs text-gray-600 mb-2 font-medium">📊 Statistics</div>
                        <div class="grid grid-cols-2 gap-3 text-xs">
                            <div class="bg-white p-2 rounded border">
                                <div class="text-gray-500">Total Sales</div>
                                <div class="font-semibold text-lg text-ghana-green">₵45,230</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="text-gray-500">Average</div>
                                <div class="font-semibold text-lg text-primary">₵2,261</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="text-gray-500">Growth</div>
                                <div class="font-semibold text-lg text-green-600">+12.5%</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="text-gray-500">Confidence</div>
                                <div class="font-semibold text-lg text-blue-600">94%</div>
                            </div>
                        </div>
                    </div>

                    <!-- Insights -->
                    <div class="space-y-2">
                        <div class="flex items-start gap-2 text-xs">
                            <span class="text-green-500 mt-0.5">✓</span>
                            <span class="text-gray-700">Strong upward trend in Q4 with 15% month-over-month growth</span>
                        </div>
                        <div class="flex items-start gap-2 text-xs">
                            <span class="text-blue-500 mt-0.5">ℹ️</span>
                            <span class="text-gray-700">Peak sales occurred in weeks 16-18, suggesting seasonal patterns</span>
                        </div>
                        <div class="flex items-start gap-2 text-xs">
                            <span class="text-orange-500 mt-0.5">⚠️</span>
                            <span class="text-gray-700">Minor dip in week 12 - consider investigating external factors</span>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="flex gap-2 mt-3 pt-3 border-t border-gray-100">
                        <button class="text-xs bg-primary/10 text-primary px-2 py-1 rounded-md hover:bg-primary/20 transition-colors">
                            📊 Create Chart
                        </button>
                        <button class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md hover:bg-gray-200 transition-colors">
                            📋 Copy Analysis
                        </button>
                        <button class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md hover:bg-gray-200 transition-colors">
                            👍 Good
                        </button>
                    </div>
                </div>
            </div>

            <!-- AI Typing Indicator -->
            <div class="flex gap-2">
                <div class="w-7 h-7 rounded-full bg-gradient-to-br from-accent to-emerald-600 flex items-center justify-center text-white text-xs font-semibold">
                    E
                </div>
                <div class="bg-white border border-gray-200 p-3 rounded-2xl rounded-bl-md text-sm shadow-sm">
                    <div class="typing-indicator">
                        <span class="text-gray-500 text-xs mr-2">Generating chart...</span>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="p-3 border-t bg-white/95 backdrop-blur">
            <div class="flex gap-2 items-end">
                <div class="flex-1">
                    <textarea 
                        class="w-full p-3 border border-gray-200 rounded-xl text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all" 
                        rows="2" 
                        placeholder="Ask me anything about your data..."
                    ></textarea>
                </div>
                
                <!-- Voice Input Button -->
                <button class="relative w-12 h-12 bg-gradient-to-br from-accent to-emerald-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all group">
                    <span class="text-lg group-hover:scale-110 transition-transform">🎤</span>
                    <div class="ripple"></div>
                </button>
                
                <!-- Send Button -->
                <button class="w-12 h-12 bg-gradient-to-br from-primary to-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all group">
                    <span class="text-lg group-hover:scale-110 transition-transform">➤</span>
                </button>
            </div>
            
            <!-- Language indicator -->
            <div class="flex items-center justify-center mt-2">
                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">🇬🇭 English • Voice ready</span>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="flex items-center justify-between p-2 border-t bg-gray-50 text-xs">
            <div class="flex items-center gap-2">
                <div class="flex items-center gap-1">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-green-700 font-medium">Online</span>
                </div>
                <span class="text-gray-400">•</span>
                <span class="text-gray-600">Last sync: 2s ago</span>
            </div>
            <div class="flex items-center gap-2">
                <span class="text-gray-600">25/100 queries</span>
                <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div class="w-1/4 h-full bg-gradient-to-r from-primary to-orange-600 rounded-full transition-all duration-500"></div>
                </div>
                <span class="text-primary">⚡</span>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate voice input animation
            const voiceBtn = document.querySelector('button[class*="accent"]');
            voiceBtn.addEventListener('click', function() {
                this.classList.add('animate-pulse');
                setTimeout(() => {
                    this.classList.remove('animate-pulse');
                }, 3000);
            });

            // Simulate typing in textarea
            const textarea = document.querySelector('textarea');
            textarea.addEventListener('focus', function() {
                this.style.borderColor = '#f97316';
            });
            textarea.addEventListener('blur', function() {
                this.style.borderColor = '#e5e7eb';
            });
        });
    </script>
</body>
</html>
