{"extends": "../../tsconfig.json", "compilerOptions": {"plugins": [{"name": "next"}], "paths": {"@/*": ["./app/*"], "@/components/*": ["./app/components/*"], "@/lib/*": ["./app/lib/*"]}, "target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}