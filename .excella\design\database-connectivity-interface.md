# Database Connectivity Interface Design
*Comprehensive UI/UX Design for Data Source Integration in Excella Excel Add-in*

## Overview

This document defines the complete database connectivity interface for Excella, enabling users to connect to multiple data sources including databases, cloud storage, and local files. The design prioritizes ease of use, security, and performance within Excel's taskpane constraints while supporting African market connectivity considerations.

**Design Audit Note**: This interface has been reviewed and updated to ensure all user-facing elements focus on user benefits rather than technical implementation details. Technical architecture decisions (like MCP integration) are handled transparently in the background without exposing complexity to users.

## Design Philosophy

### Core Principles
1. **Progressive Disclosure**: Start simple, reveal complexity as needed
2. **Security First**: Secure credential handling and encrypted connections
3. **Offline Resilience**: Graceful degradation for poor connectivity
4. **Visual Clarity**: Clear status indicators and error messaging
5. **Excel Integration**: Seamless integration with Excel workflows
6. **Hybrid Architecture**: Traditional drivers for performance, MCP for AI-enhanced queries

### Supported Data Sources

#### Traditional Database Drivers (Phase 1 - MVP)
- **Databases**: PostgreSQL, MySQL, Microsoft SQL Server, Oracle, Supabase
- **Business Systems**: Salesforce CRM, Zoho CRM, QuickBooks Online
- **Analytics Platforms**: Snowflake Data Warehouse
- **Cloud Storage**: OneDrive, Google Drive, Google Sheets
- **Local Files**: Excel, CSV, PDF files

#### Model Context Protocol Integration (Phase 2 - AI Enhancement)
- **MCP Servers**: Custom MCP servers for each data source
- **AI-Mediated Access**: Natural language database queries
- **Cross-Source Analytics**: Multi-database AI analysis
- **Dynamic Schema Discovery**: Automatic database exploration

## Interface Architecture

### 1. Database Connection Entry Point

#### Option A: Header Integration (Recommended)
```typescript
// Add database icon to existing header controls
<div className="flex items-center gap-1">
  <Button size="icon" variant="ghost" className="h-6 w-6" onClick={onDatabaseClick}>
    <Database className="h-3 w-3" />
  </Button>
  <Button size="icon" variant="ghost" className="h-6 w-6">
    <Plus className="h-3 w-3" />
  </Button>
  // ... existing controls
</div>
```

#### Option B: Input Area Integration
```typescript
// Add database button next to Agent/Chat toggle
<div className="flex items-center gap-2">
  <Button size="sm" variant="outline" onClick={onDatabaseClick}>
    <Database className="h-3 w-3 mr-1" />
    Data
  </Button>
  <Select value={agentMode} onValueChange={onModeChange}>
    // ... existing toggle
  </Select>
</div>
```

#### Decision: Header Integration
- **Rationale**: Maintains clean input area, follows existing pattern
- **Accessibility**: Clear icon with tooltip "Connect to data sources"
- **Visual Hierarchy**: Consistent with other header controls

### 2. Data Source Selection Interface

#### Source Categories
```typescript
interface DataSourceCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType;
  sources: DataSource[];
}

const dataSourceCategories: DataSourceCategory[] = [
  {
    id: 'databases',
    title: 'Databases',
    description: 'Connect to SQL databases',
    icon: Database,
    sources: [
      { id: 'postgresql', name: 'PostgreSQL', icon: PostgreSQLIcon },
      { id: 'mysql', name: 'MySQL', icon: MySQLIcon },
      { id: 'mssql', name: 'SQL Server', icon: SQLServerIcon },
      { id: 'oracle', name: 'Oracle', icon: OracleIcon },
      { id: 'supabase', name: 'Supabase', icon: SupabaseIcon }
    ]
  },
  {
    id: 'business',
    title: 'Business Systems',
    description: 'CRM, ERP, and accounting systems',
    icon: Building,
    sources: [
      { id: 'salesforce', name: 'Salesforce', icon: SalesforceIcon },
      { id: 'zoho', name: 'Zoho CRM', icon: ZohoIcon },
      { id: 'quickbooks', name: 'QuickBooks', icon: QuickBooksIcon }
    ]
  },
  {
    id: 'analytics',
    title: 'Analytics Platforms',
    description: 'Data warehouses and analytics',
    icon: BarChart,
    sources: [
      { id: 'snowflake', name: 'Snowflake', icon: SnowflakeIcon }
    ]
  },
  {
    id: 'cloud',
    title: 'Cloud Storage',
    description: 'Access cloud files and sheets',
    icon: Cloud,
    sources: [
      { id: 'onedrive', name: 'OneDrive', icon: OneDriveIcon },
      { id: 'googledrive', name: 'Google Drive', icon: GoogleDriveIcon },
      { id: 'googlesheets', name: 'Google Sheets', icon: GoogleSheetsIcon }
    ]
  },
  {
    id: 'files',
    title: 'Local Files',
    description: 'Upload and analyze files',
    icon: FileText,
    sources: [
      { id: 'excel', name: 'Excel Files', icon: FileSpreadsheet },
      { id: 'csv', name: 'CSV Files', icon: FileText },
      { id: 'pdf', name: 'PDF Files', icon: FileText }
    ]
  }
];
```

#### Source Selection Modal
```jsx
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogContent className="max-w-md">
    <DialogHeader>
      <DialogTitle>Connect to Data Source</DialogTitle>
      <DialogDescription>
        Choose a data source to connect and analyze your data
      </DialogDescription>
    </DialogHeader>
    
    <div className="space-y-4">
      {dataSourceCategories.map((category) => (
        <div key={category.id} className="space-y-2">
          <div className="flex items-center gap-2">
            <category.icon className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-medium text-sm">{category.title}</h3>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            {category.sources.map((source) => (
              <Button
                key={source.id}
                variant="outline"
                className="h-auto p-3 flex flex-col items-center gap-2"
                onClick={() => handleSourceSelect(source)}
              >
                <source.icon className="h-6 w-6" />
                <span className="text-xs">{source.name}</span>
              </Button>
            ))}
          </div>
        </div>
      ))}
    </div>
  </DialogContent>
</Dialog>
```

### 3. Connection Setup Flow

#### Step 1: Connection Configuration
```typescript
interface ConnectionConfig {
  sourceType: string;
  name: string;
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  connectionString?: string;
  authMethod: 'credentials' | 'oauth' | 'token';
}

// Database Connection Form
export function DatabaseConnectionForm({ sourceType, onSubmit }: {
  sourceType: string;
  onSubmit: (config: ConnectionConfig) => void;
}) {
  return (
    <Form {...form}>
      <div className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Connection Name</FormLabel>
              <FormControl>
                <Input placeholder="My Database" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        
        {(sourceType === 'postgresql' || sourceType === 'mssql') && (
          <>
            <FormField
              control={form.control}
              name="host"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Host</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={sourceType === 'postgresql' ? 'localhost' : 'server.database.windows.net'}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-2">
              <FormField
                control={form.control}
                name="port"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Port</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={sourceType === 'postgresql' ? '5432' : '1433'}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="database"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Database</FormLabel>
                    <FormControl>
                      <Input placeholder="mydb" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {(sourceType === 'salesforce' || sourceType === 'zoho' || sourceType === 'quickbooks') && (
          <>
            <FormField
              control={form.control}
              name="authMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Authentication Method</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue="oauth">
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select authentication method" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="oauth">OAuth 2.0</SelectItem>
                      {sourceType === 'salesforce' && (
                        <SelectItem value="token">Access Token</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            {sourceType === 'salesforce' && (
              <FormField
                control={form.control}
                name="instanceUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instance URL</FormLabel>
                    <FormControl>
                      <Input placeholder="https://yourinstance.salesforce.com" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}
          </>
        )}

        {sourceType === 'snowflake' && (
          <>
            <FormField
              control={form.control}
              name="account"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Identifier</FormLabel>
                  <FormControl>
                    <Input placeholder="xy12345.us-east-1" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-2">
              <FormField
                control={form.control}
                name="warehouse"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Warehouse</FormLabel>
                    <FormControl>
                      <Input placeholder="COMPUTE_WH" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="database"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Database</FormLabel>
                    <FormControl>
                      <Input placeholder="DEMO_DB" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="schema"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Schema</FormLabel>
                  <FormControl>
                    <Input placeholder="PUBLIC" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </>
        )}
        
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </Form>
  );
}
```

#### Step 2: Connection Testing
```typescript
interface ConnectionTestResult {
  success: boolean;
  message: string;
  latency?: number;
  details?: {
    serverVersion?: string;
    databaseSize?: string;
    tableCount?: number;
  };
}

export function ConnectionTestPanel({ config, onTest }: {
  config: ConnectionConfig;
  onTest: (config: ConnectionConfig) => Promise<ConnectionTestResult>;
}) {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<ConnectionTestResult | null>(null);
  
  const handleTest = async () => {
    setTesting(true);
    try {
      const testResult = await onTest(config);
      setResult(testResult);
    } finally {
      setTesting(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Test Connection</h3>
        <Button 
          size="sm" 
          onClick={handleTest} 
          disabled={testing}
        >
          {testing ? (
            <>
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              <Zap className="h-3 w-3 mr-1" />
              Test
            </>
          )}
        </Button>
      </div>
      
      {result && (
        <Alert variant={result.success ? "default" : "destructive"}>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>
            {result.success ? "Connection Successful" : "Connection Failed"}
          </AlertTitle>
          <AlertDescription>
            {result.message}
            {result.latency && (
              <div className="mt-1 text-xs">
                Response time: {result.latency}ms
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
```

### 4. Connection Status Indicators

#### Status Types and Visual Design
```typescript
type ConnectionStatus =
  | 'disconnected'  // Not connected
  | 'connecting'    // Establishing connection
  | 'connected'     // Successfully connected
  | 'error'         // Connection error
  | 'testing'       // Testing connection
  | 'syncing';      // Syncing data

interface ConnectionStatusProps {
  status: ConnectionStatus;
  lastSync?: Date;
  errorMessage?: string;
}

export function ConnectionStatusIndicator({ status, lastSync, errorMessage }: ConnectionStatusProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'disconnected':
        return {
          icon: Circle,
          color: 'text-muted-foreground',
          bgColor: 'bg-muted-foreground/20',
          label: 'Disconnected'
        };
      case 'connecting':
        return {
          icon: Loader2,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          label: 'Connecting...',
          animate: true
        };
      case 'connected':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          label: 'Connected'
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-destructive',
          bgColor: 'bg-destructive/10',
          label: 'Error'
        };
      case 'testing':
        return {
          icon: Zap,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          label: 'Testing...',
          animate: true
        };
      case 'syncing':
        return {
          icon: RefreshCw,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          label: 'Syncing...',
          animate: true
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className="flex items-center gap-2">
      <div className={`w-6 h-6 rounded-full ${config.bgColor} flex items-center justify-center`}>
        <Icon className={`h-3 w-3 ${config.color} ${config.animate ? 'animate-spin' : ''}`} />
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium">{config.label}</div>
        {lastSync && status === 'connected' && (
          <div className="text-xs text-muted-foreground">
            Last sync: {formatDistanceToNow(lastSync)} ago
          </div>
        )}
        {errorMessage && status === 'error' && (
          <div className="text-xs text-destructive truncate">
            {errorMessage}
          </div>
        )}
      </div>
    </div>
  );
}
```

#### Live Status in Header
```typescript
// Integration with existing header Live indicator
export function HeaderWithDatabaseStatus({
  isLive,
  connections,
  onDatabaseClick
}: {
  isLive: boolean;
  connections: Connection[];
  onDatabaseClick: () => void;
}) {
  const activeConnections = connections.filter(c => c.status === 'connected').length;
  const hasErrors = connections.some(c => c.status === 'error');

  return (
    <div className="flex items-center justify-between p-4 border-b border-border">
      <div className="flex items-center gap-3">
        {/* Existing Live indicator */}
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm font-medium text-foreground">Live</span>
        </div>

        {/* Database connection status */}
        {activeConnections > 0 && (
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3 text-green-600" />
            <span className="text-xs text-muted-foreground">
              {activeConnections} connected
            </span>
          </div>
        )}

        {hasErrors && (
          <div className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3 text-destructive" />
            <span className="text-xs text-destructive">Connection issues</span>
          </div>
        )}
      </div>

      <div className="flex items-center gap-1">
        <Button
          size="icon"
          variant="ghost"
          className="h-6 w-6"
          onClick={onDatabaseClick}
        >
          <Database className="h-3 w-3" />
        </Button>
        {/* ... existing controls */}
      </div>
    </div>
  );
}
```

### 5. Error Handling & Troubleshooting

#### Error Types and Messages
```typescript
interface ConnectionError {
  type: 'network' | 'authentication' | 'permission' | 'timeout' | 'configuration';
  message: string;
  suggestions: string[];
  canRetry: boolean;
  documentation?: string;
}

const errorHandlers: Record<string, (error: any) => ConnectionError> = {
  'ECONNREFUSED': () => ({
    type: 'network',
    message: 'Unable to connect to the database server',
    suggestions: [
      'Check if the database server is running',
      'Verify the host and port are correct',
      'Check your network connection'
    ],
    canRetry: true,
    documentation: '/docs/troubleshooting/connection-refused'
  }),

  'EAUTH': () => ({
    type: 'authentication',
    message: 'Authentication failed',
    suggestions: [
      'Verify your username and password',
      'Check if the user has database access',
      'Ensure the authentication method is correct'
    ],
    canRetry: true,
    documentation: '/docs/troubleshooting/authentication'
  }),

  'ETIMEOUT': () => ({
    type: 'timeout',
    message: 'Connection timed out',
    suggestions: [
      'Check your internet connection',
      'Try connecting to a closer server',
      'Increase the connection timeout'
    ],
    canRetry: true,
    documentation: '/docs/troubleshooting/timeout'
  })
};
```

#### Error Display Component
```typescript
export function ConnectionErrorPanel({
  error,
  onRetry,
  onDismiss
}: {
  error: ConnectionError;
  onRetry: () => void;
  onDismiss: () => void;
}) {
  return (
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Connection Error</AlertTitle>
      <AlertDescription>
        <div className="space-y-3">
          <p>{error.message}</p>

          <div>
            <h4 className="font-medium text-sm mb-1">Suggested solutions:</h4>
            <ul className="text-sm space-y-1">
              {error.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex items-center gap-2">
            {error.canRetry && (
              <Button size="sm" variant="outline" onClick={onRetry}>
                <RotateCcw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            )}

            {error.documentation && (
              <Button size="sm" variant="outline" asChild>
                <a href={error.documentation} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Help
                </a>
              </Button>
            )}

            <Button size="sm" variant="ghost" onClick={onDismiss}>
              Dismiss
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}
```

### 6. Connection Management Panel

#### Connection List Interface
```typescript
interface Connection {
  id: string;
  name: string;
  type: string;
  status: ConnectionStatus;
  lastUsed: Date;
  config: ConnectionConfig;
  metadata?: {
    tableCount?: number;
    recordCount?: number;
    size?: string;
  };
}

export function ConnectionManagementPanel({
  connections,
  onEdit,
  onDelete,
  onConnect,
  onDisconnect
}: {
  connections: Connection[];
  onEdit: (connection: Connection) => void;
  onDelete: (connectionId: string) => void;
  onConnect: (connectionId: string) => void;
  onDisconnect: (connectionId: string) => void;
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Data Connections</h3>
        <Button size="sm" variant="outline">
          <Plus className="h-3 w-3 mr-1" />
          Add Connection
        </Button>
      </div>

      {connections.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No data connections configured</p>
          <p className="text-xs">Add a connection to get started</p>
        </div>
      ) : (
        <div className="space-y-2">
          {connections.map((connection) => (
            <Card key={connection.id} className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <ConnectionStatusIndicator
                    status={connection.status}
                    lastSync={connection.lastUsed}
                  />

                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {connection.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {connection.type} • Last used {formatDistanceToNow(connection.lastUsed)} ago
                    </div>
                    {connection.metadata && (
                      <div className="text-xs text-muted-foreground">
                        {connection.metadata.tableCount} tables • {connection.metadata.size}
                      </div>
                    )}
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="icon" variant="ghost" className="h-6 w-6">
                      <MoreVertical className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {connection.status === 'connected' ? (
                      <DropdownMenuItem onClick={() => onDisconnect(connection.id)}>
                        <Unplug className="h-3 w-3 mr-2" />
                        Disconnect
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem onClick={() => onConnect(connection.id)}>
                        <Plug className="h-3 w-3 mr-2" />
                        Connect
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={() => onEdit(connection)}>
                      <Edit className="h-3 w-3 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onDelete(connection.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-3 w-3 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 7. Data Preview & Selection

#### Table/Schema Browser
```typescript
interface DatabaseSchema {
  tables: DatabaseTable[];
  views: DatabaseView[];
  functions?: DatabaseFunction[];
}

interface DatabaseTable {
  name: string;
  schema: string;
  rowCount: number;
  columns: DatabaseColumn[];
  description?: string;
}

interface DatabaseColumn {
  name: string;
  type: string;
  nullable: boolean;
  primaryKey: boolean;
  description?: string;
}

export function DataPreviewPanel({
  schema,
  onTableSelect,
  selectedTables
}: {
  schema: DatabaseSchema;
  onTableSelect: (table: DatabaseTable) => void;
  selectedTables: string[];
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'tables' | 'views'>('tables');

  const filteredItems = schema[selectedCategory].filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search tables..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1"
          />
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="tables">Tables</SelectItem>
              <SelectItem value="views">Views</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2 max-h-64 overflow-y-auto">
        {filteredItems.map((item) => (
          <Card
            key={`${item.schema}.${item.name}`}
            className={`p-3 cursor-pointer transition-colors ${
              selectedTables.includes(item.name)
                ? 'bg-accent border-primary'
                : 'hover:bg-accent/50'
            }`}
            onClick={() => onTableSelect(item)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">
                  {item.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {item.schema} • {item.rowCount.toLocaleString()} rows
                </div>
                {item.description && (
                  <div className="text-xs text-muted-foreground mt-1 truncate">
                    {item.description}
                  </div>
                )}
              </div>

              {selectedTables.includes(item.name) && (
                <CheckCircle className="h-4 w-4 text-primary" />
              )}
            </div>
          </Card>
        ))}
      </div>

      {selectedTables.length > 0 && (
        <div className="pt-2 border-t">
          <div className="text-sm font-medium mb-2">
            Selected: {selectedTables.length} table{selectedTables.length !== 1 ? 's' : ''}
          </div>
          <div className="flex flex-wrap gap-1">
            {selectedTables.map((tableName) => (
              <Badge key={tableName} variant="secondary" className="text-xs">
                {tableName}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

### 8. African Market Optimizations

#### Connectivity Resilience
```typescript
// Enhanced connection manager for African markets
export class AfricanMarketConnectionManager {
  private connectionQuality: 'fast' | 'slow' | 'offline' = 'fast';
  private retryAttempts = 0;
  private maxRetries = 3;

  async establishConnection(config: ConnectionConfig): Promise<Connection> {
    try {
      // Assess connection quality first
      await this.assessConnectionQuality();

      // Adjust timeout based on connection quality
      const timeout = this.getTimeoutForQuality();

      // Attempt connection with progressive timeout
      return await this.connectWithTimeout(config, timeout);
    } catch (error) {
      return this.handleConnectionFailure(error, config);
    }
  }

  private async assessConnectionQuality(): Promise<void> {
    try {
      const startTime = performance.now();
      await fetch('/api/ping', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      const latency = performance.now() - startTime;

      if (latency > 2000) {
        this.connectionQuality = 'slow';
      } else {
        this.connectionQuality = 'fast';
      }
    } catch {
      this.connectionQuality = 'offline';
    }
  }

  private getTimeoutForQuality(): number {
    switch (this.connectionQuality) {
      case 'fast': return 10000;   // 10 seconds
      case 'slow': return 30000;   // 30 seconds
      case 'offline': return 60000; // 1 minute
    }
  }

  private async handleConnectionFailure(
    error: any,
    config: ConnectionConfig
  ): Promise<Connection> {
    this.retryAttempts++;

    if (this.retryAttempts < this.maxRetries) {
      // Progressive backoff: 2s, 5s, 10s
      const delay = Math.pow(2, this.retryAttempts) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));

      return this.establishConnection(config);
    }

    // Offer offline mode for local data analysis
    throw new ConnectionError({
      type: 'network',
      message: 'Unable to establish connection after multiple attempts',
      suggestions: [
        'Check your internet connection',
        'Try again in a few minutes',
        'Use offline mode for local data analysis'
      ],
      canRetry: true,
      offlineMode: true
    });
  }
}
```

#### Offline Mode Integration
```typescript
export function OfflineModeNotification({
  onEnableOffline,
  onRetryConnection
}: {
  onEnableOffline: () => void;
  onRetryConnection: () => void;
}) {
  return (
    <Alert>
      <Wifi className="h-4 w-4" />
      <AlertTitle>Connection Issues Detected</AlertTitle>
      <AlertDescription>
        <div className="space-y-3">
          <p>
            We're having trouble connecting to your data source.
            You can continue working with local data analysis.
          </p>

          <div className="flex items-center gap-2">
            <Button size="sm" onClick={onEnableOffline}>
              <Download className="h-3 w-3 mr-1" />
              Work Offline
            </Button>
            <Button size="sm" variant="outline" onClick={onRetryConnection}>
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry Connection
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            Offline mode uses Pyodide for local Python execution
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}
```

## Connection Setup Options

### User-Friendly Connection Methods

#### Quick Connect (Recommended)
**For most users**: Simple setup with guided configuration and automatic optimization.

**Benefits**:
- ✅ Fastest setup time (under 2 minutes)
- ✅ Automatic performance optimization
- ✅ Built-in error detection and recovery
- ✅ Works great for standard business data sources
- ✅ Optimized for African market connectivity

#### Advanced Setup
**For power users**: Full control over connection parameters and advanced features.

**Benefits**:
- ✅ Custom connection strings and parameters
- ✅ Advanced security configurations
- ✅ Multiple authentication methods
- ✅ Custom timeout and retry settings
- ✅ Detailed connection diagnostics

### Connection Setup Interface

#### Setup Method Selection
```typescript
interface ConnectionSetupOptions {
  setupType: 'quick' | 'advanced';
  optimizeFor: 'speed' | 'reliability' | 'security';
  region: 'ghana' | 'nigeria' | 'international';
}

export function ConnectionSetupSelector({ onSelect }: {
  onSelect: (options: ConnectionSetupOptions) => void;
}) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-medium mb-2">Choose Setup Method</h3>
        <RadioGroup defaultValue="quick">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="quick" id="quick" />
            <Label htmlFor="quick">
              <div>
                <div className="font-medium">Quick Connect (Recommended)</div>
                <div className="text-sm text-muted-foreground">
                  Fast setup with automatic optimization
                </div>
              </div>
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="advanced" id="advanced" />
            <Label htmlFor="advanced">
              <div>
                <div className="font-medium">Advanced Setup</div>
                <div className="text-sm text-muted-foreground">
                  Full control over connection parameters
                </div>
              </div>
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div>
        <h3 className="font-medium mb-2">Optimize For</h3>
        <RadioGroup defaultValue="speed">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="speed" id="speed" />
            <Label htmlFor="speed">Speed (Fastest data access)</Label>
          </div>

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="reliability" id="reliability" />
            <Label htmlFor="reliability">Reliability (Most stable connection)</Label>
          </div>

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="security" id="security" />
            <Label htmlFor="security">Security (Enhanced data protection)</Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
}
```

## Implementation Guidelines

### Security Considerations
1. **Credential Storage**: Use secure browser storage APIs
2. **Connection Encryption**: Enforce SSL/TLS for all connections
3. **Input Validation**: Sanitize all user inputs
4. **Access Control**: Implement proper authentication checks

### Performance Optimization
1. **Connection Pooling**: Reuse database connections
2. **Lazy Loading**: Load schema information on demand
3. **Caching**: Cache connection metadata locally
4. **Debouncing**: Debounce connection tests and searches

### Accessibility Features
1. **Keyboard Navigation**: Full keyboard support for all interactions
2. **Screen Reader Support**: Proper ARIA labels and descriptions
3. **High Contrast**: Support for high contrast themes
4. **Focus Management**: Clear focus indicators and logical tab order

### Testing Strategy
1. **Connection Testing**: Automated tests for all supported data sources
2. **Error Scenarios**: Test various failure modes and recovery
3. **Performance Testing**: Test with slow connections and large datasets
4. **Accessibility Testing**: Verify screen reader and keyboard navigation

## Integration Points

### Excel Office.js Integration
```typescript
// Insert data from connected source into Excel
export async function insertDataToExcel(
  data: any[],
  targetRange?: string
): Promise<void> {
  await Excel.run(async (context) => {
    const worksheet = context.workbook.worksheets.getActiveWorksheet();
    const range = targetRange
      ? worksheet.getRange(targetRange)
      : worksheet.getUsedRange();

    // Convert data to Excel-compatible format
    const excelData = convertToExcelFormat(data);

    range.values = excelData;
    range.format.autofitColumns();

    await context.sync();
  });
}
```

### Supabase Integration
```typescript
// Sync connection configurations with Supabase
export class ConnectionConfigSync {
  private supabase: SupabaseClient;

  async saveConnection(config: ConnectionConfig): Promise<void> {
    const { data, error } = await this.supabase
      .from('user_connections')
      .upsert({
        user_id: await this.getCurrentUserId(),
        name: config.name,
        type: config.sourceType,
        config: this.encryptConfig(config),
        created_at: new Date()
      });

    if (error) throw error;
  }

  async loadConnections(): Promise<ConnectionConfig[]> {
    const { data, error } = await this.supabase
      .from('user_connections')
      .select('*')
      .eq('user_id', await this.getCurrentUserId());

    if (error) throw error;

    return data.map(row => this.decryptConfig(row.config));
  }
}
```

---

*This comprehensive database connectivity interface design ensures secure, performant, and user-friendly data source integration within Excella's Excel add-in, optimized for African markets while maintaining global accessibility standards.*
