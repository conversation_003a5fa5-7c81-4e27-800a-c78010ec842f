# Data Models & System Architecture Plan
*Comprehensive Database Schema and System Architecture for Excella MVP - December 2024*

## Executive Summary

This plan defines the complete data models, database schema, and system architecture for Excella MVP. It establishes the foundation for all features including user management, AI conversations, wallet-based billing, database connectivity, and cross-platform synchronization between Excel add-in and web application.

### 🎯 **Key Architecture Decisions**
- **Database**: Supabase PostgreSQL 15.x with Row Level Security (RLS)
- **API Layer**: tRPC 11.2.0 with type-safe contracts
- **Authentication**: Supabase Auth with @supabase/ssr
- **Real-time**: Supabase Realtime for live synchronization
- **AI Integration**: Agno 1.2.2 with intelligent orchestrator architecture (Gemini 2.5 Pro as main orchestrator, DeepSeek Coder, Gemini Flash, DeepSeek R1-0528 as specialists)
- **Sandbox Strategy**: Hybrid Pyodide (WebAssembly) + E2B Code Interpreter with intelligent routing
- **Payment Priority**: Paystack (primary), Flutterwave (secondary), Stripe (backup)

---

## Phase A: Core Data Models (Week 1)

### A.1 User Management Schema

#### Users Table (Updated to Match Migration)
```sql
-- Core user information with embedded wallet-based tracking
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Wallet Information (Embedded in Users Table)
  wallet_balance_cents INTEGER DEFAULT 500, -- Default $5.00 starting balance
  total_spent_cents INTEGER DEFAULT 0,
  total_loaded_cents INTEGER DEFAULT 500, -- Track total money loaded
  low_balance_threshold_cents INTEGER DEFAULT 100, -- $1.00 warning threshold
  auto_reload_enabled BOOLEAN DEFAULT FALSE,
  auto_reload_amount_cents INTEGER DEFAULT 500, -- $5.00 default reload

  -- Usage Tracking (Real-time cost tracking)
  total_queries_count INTEGER DEFAULT 0,
  total_tokens_used INTEGER DEFAULT 0,
  last_query_at TIMESTAMPTZ,

  -- Regional Settings (African Market Focus)
  currency TEXT DEFAULT 'USD' CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),
  timezone TEXT DEFAULT 'UTC',
  language TEXT DEFAULT 'en' CHECK (language IN ('en', 'fr')),

  -- Platform Preferences
  preferred_platform TEXT DEFAULT 'excel' CHECK (preferred_platform IN ('excel', 'web')),
  onboarding_completed BOOLEAN DEFAULT FALSE,

  -- Cost Transparency Settings
  show_cost_estimates BOOLEAN DEFAULT TRUE, -- User preference for cost visibility
  cost_alert_threshold_cents INTEGER DEFAULT 50, -- Alert when query costs > $0.50

  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
  CONSTRAINT positive_wallet_balance CHECK (wallet_balance_cents >= 0)
);

-- Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid() = id);
```

#### Teams Table (Updated to Match Migration)
```sql
-- Team management for collaborative features (volume pricing, admin controls)
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Team Wallet & Volume Pricing
  shared_wallet_balance_cents INTEGER DEFAULT 0, -- Optional shared team wallet
  volume_discount_percent INTEGER DEFAULT 0, -- Volume pricing discount (0-30%)
  max_members INTEGER DEFAULT 5,
  current_members INTEGER DEFAULT 1,

  -- Team Settings
  shared_templates JSONB DEFAULT '[]',
  team_preferences JSONB DEFAULT '{}',
  admin_controls JSONB DEFAULT '{"cost_limits": false, "usage_analytics": true}',

  -- Usage Analytics
  total_team_spent_cents INTEGER DEFAULT 0,
  total_team_queries INTEGER DEFAULT 0,

  CONSTRAINT min_team_size CHECK (max_members >= 5) -- Minimum 5 users for Team features
);

-- Team Members Junction Table
CREATE TABLE team_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(team_id, user_id)
);
```

### A.2 AI Conversation Schema

#### Conversations Table
```sql
-- AI conversation sessions
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Conversation Context
  platform TEXT NOT NULL CHECK (platform IN ('excel', 'web')),
  excel_workbook_id TEXT, -- Excel workbook identifier
  data_sources JSONB DEFAULT '[]', -- Connected databases/files
  
  -- AI Configuration
  agent_mode TEXT DEFAULT 'chat' CHECK (agent_mode IN ('chat', 'agent')),
  active_agents JSONB DEFAULT '["DataAnalyst"]',
  
  -- Status
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
  message_count INTEGER DEFAULT 0,
  
  -- Performance Tracking
  total_tokens_used INTEGER DEFAULT 0,
  total_cost_usd DECIMAL(10,4) DEFAULT 0.0000
);

-- Conversation Messages
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Message Content
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'analysis', 'visualization', 'code', 'error')),
  
  -- AI Metadata (Excella Orchestrator Architecture)
  agent_name TEXT, -- 'DataAnalyst', 'Visualizer', 'CodeExecutor'
  model_used TEXT, -- 'gemini-2.5-pro', 'deepseek-coder', 'gemini-flash', 'deepseek-r1-0528'
  tokens_used INTEGER DEFAULT 0,
  cost_usd DECIMAL(8,4) DEFAULT 0.0000,
  execution_time_ms INTEGER,
  confidence_score DECIMAL(3,2),
  sandbox_method TEXT CHECK (sandbox_method IN ('pyodide', 'e2b', 'hybrid')), -- Execution method used
  
  -- Results Data
  result_data JSONB, -- Analysis results, charts, code output
  attachments JSONB DEFAULT '[]', -- File attachments, images
  
  -- User Interaction
  feedback TEXT CHECK (feedback IN ('positive', 'negative', 'neutral')),
  feedback_comment TEXT,
  regenerated_from UUID REFERENCES messages(id)
);
```

### A.3 Database Connectivity Schema

#### Data Connections Table
```sql
-- User's database and data source connections
CREATE TABLE data_connections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Connection Details
  name TEXT NOT NULL, -- User-friendly name
  connection_type TEXT NOT NULL CHECK (connection_type IN (
    'postgresql', 'mysql', 'mssql', 'oracle', 'supabase',
    'salesforce', 'zoho', 'quickbooks', 'snowflake',
    'onedrive', 'googledrive', 'googlesheets', 'local_file'
  )),
  
  -- Connection Configuration (encrypted)
  connection_config JSONB NOT NULL, -- Encrypted connection details
  
  -- Status & Health
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'error')),
  last_tested TIMESTAMPTZ,
  last_used TIMESTAMPTZ,
  error_message TEXT,
  
  -- Usage Statistics
  query_count INTEGER DEFAULT 0,
  data_volume_mb DECIMAL(10,2) DEFAULT 0.00,
  
  -- Access Method (Phase 1: Direct, Phase 2: MCP)
  access_method TEXT DEFAULT 'direct' CHECK (access_method IN ('direct', 'mcp', 'hybrid'))
);
```

---

## Phase B: System Architecture (Week 2)

### B.1 API Architecture with tRPC

#### Core API Structure
```typescript
// tRPC Router Definition
export const appRouter = router({
  // Authentication & User Management
  auth: authRouter,
  user: userRouter,
  team: teamRouter,
  
  // AI & Conversations
  conversation: conversationRouter,
  message: messageRouter,
  ai: aiRouter,
  
  // Data & Analytics
  dataConnection: dataConnectionRouter,
  analytics: analyticsRouter,
  
  // Wallet & Billing
  wallet: walletRouter,
  billing: billingRouter,
  
  // Platform Integration
  excel: excelRouter,
  sync: syncRouter
});

export type AppRouter = typeof appRouter;
```

#### User Management Router
```typescript
export const userRouter = router({
  // Profile Management
  getProfile: protectedProcedure
    .query(async ({ ctx }) => {
      return await ctx.supabase
        .from('users')
        .select('*')
        .eq('id', ctx.user.id)
        .single();
    }),
    
  updateProfile: protectedProcedure
    .input(z.object({
      full_name: z.string().optional(),
      language: z.enum(['en', 'fr']).optional(),
      timezone: z.string().optional(),
      preferred_platform: z.enum(['excel', 'web']).optional()
    }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.supabase
        .from('users')
        .update({ ...input, updated_at: new Date() })
        .eq('id', ctx.user.id);
    }),
    
  // Wallet & Usage Tracking (Updated for Users Table)
  getWalletStats: protectedProcedure
    .query(async ({ ctx }) => {
      const { data: user } = await ctx.supabase
        .from('users')
        .select(`
          total_queries_count, total_spent_cents, total_loaded_cents,
          wallet_balance_cents, total_tokens_used, last_query_at,
          show_cost_estimates, cost_alert_threshold_cents
        `)
        .eq('id', ctx.user.id)
        .single();

      return {
        queriesUsed: user?.total_queries_count || 0,
        totalSpent: user?.total_spent_cents || 0,
        totalLoaded: user?.total_loaded_cents || 500, // Default $5.00
        walletBalance: user?.wallet_balance_cents || 500,
        totalTokens: user?.total_tokens_used || 0,
        lastQuery: user?.last_query_at,
        showCostEstimates: user?.show_cost_estimates ?? true,
        costAlertThreshold: user?.cost_alert_threshold_cents || 50
      };
    }),
    
  deductFromWallet: protectedProcedure
    .input(z.object({
      costCents: z.number(),
      tokensUsed: z.number(),
      modelUsed: z.enum(['gemini-2.5-pro', 'deepseek-coder', 'gemini-flash', 'deepseek-r1-0528']),
      queryId: z.string().optional(),
      description: z.string(),
      inputTokens: z.number().optional(),
      outputTokens: z.number().optional(),
      conversationId: z.string().optional(),
      messageId: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      // Check wallet balance using stored procedure
      const { data: hasBalance } = await ctx.supabase
        .rpc('check_wallet_balance', {
          user_uuid: ctx.user.id,
          estimated_cost_cents: input.costCents
        });

      if (!hasBalance) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient wallet balance. Please add funds to continue.'
        });
      }

      // Process wallet transaction using stored procedure
      return await ctx.supabase.rpc('process_wallet_transaction', {
        user_uuid: ctx.user.id,
        amount_cents: -input.costCents, // Negative for spend
        transaction_type: 'spend',
        description: input.description,
        conversation_id: input.conversationId,
        message_id: input.messageId,
        model_used: input.modelUsed,
        tokens_used: input.tokensUsed
      });
    })
});
```

### B.2 Authentication Architecture

#### Cross-Platform Authentication Flow
```typescript
// Authentication Manager for Excel + Web
export class AuthenticationManager {
  private supabase: SupabaseClient;
  
  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  }
  
  // Web Authentication (Full OAuth)
  async authenticateWeb(provider: 'google' | 'microsoft' = 'google') {
    const { data, error } = await this.supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });
    
    return { data, error };
  }
  
  // Excel Authentication (Token-based)
  async authenticateExcel(webToken: string) {
    // Validate web token and create Excel session
    const { data: session, error } = await this.supabase.auth.getSession();
    
    if (error || !session) {
      throw new Error('Invalid authentication token');
    }
    
    // Store Excel-specific session data
    await this.supabase
      .from('user_sessions')
      .upsert({
        user_id: session.user.id,
        platform: 'excel',
        session_token: webToken,
        last_active: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      });
      
    return session;
  }
  
  // Session Synchronization
  async syncSession(platform: 'web' | 'excel') {
    const { data: session } = await this.supabase.auth.getSession();
    
    if (session?.user) {
      // Update user's last active platform
      await this.supabase
        .from('users')
        .update({ 
          preferred_platform: platform,
          updated_at: new Date()
        })
        .eq('id', session.user.id);
        
      // Sync user preferences across platforms
      return await this.getUserPreferences(session.user.id);
    }
    
    return null;
  }
}
```

### B.3 Wallet-Based Billing Schema (Updated for Embedded Wallet)

#### Wallet Transactions Table (Matches Migration)
```sql
-- Wallet transactions for all money movements (loads, spends, refunds)
CREATE TABLE wallet_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE SET NULL, -- For team wallet transactions
  created_at TIMESTAMPTZ DEFAULT NOW(),

  -- Transaction Details
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('load', 'spend', 'refund', 'bonus', 'team_transfer')),
  amount_cents INTEGER NOT NULL, -- Positive for credits, negative for debits
  balance_after_cents INTEGER NOT NULL, -- Wallet balance after this transaction

  -- Transaction Context
  description TEXT NOT NULL, -- Human-readable description
  reference_id TEXT, -- External payment reference or internal query ID

  -- AI Query Context (for 'spend' transactions)
  conversation_id UUID REFERENCES conversations(id) ON DELETE SET NULL,
  message_id UUID REFERENCES messages(id) ON DELETE SET NULL,
  model_used TEXT, -- AI model that generated the cost
  tokens_used INTEGER DEFAULT 0,
  execution_time_ms INTEGER,

  -- Payment Provider (for 'load' transactions)
  provider TEXT CHECK (provider IN ('paystack', 'flutterwave', 'stripe', 'manual')),
  provider_transaction_id TEXT,
  provider_fee_cents INTEGER DEFAULT 0,

  -- Regional Settings
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),
  exchange_rate DECIMAL(10,6) DEFAULT 1.0, -- For non-USD currencies

  -- Status
  status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  failure_reason TEXT,

  CONSTRAINT valid_amount CHECK (
    (transaction_type = 'spend' AND amount_cents < 0) OR
    (transaction_type IN ('load', 'refund', 'bonus', 'team_transfer') AND amount_cents > 0)
  )
);

-- Payment Methods for wallet loading (stored payment methods)
CREATE TABLE payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Payment Method Details
  provider TEXT NOT NULL CHECK (provider IN ('paystack', 'flutterwave', 'stripe')),
  provider_customer_id TEXT NOT NULL,
  provider_payment_method_id TEXT NOT NULL,

  -- Method Information
  method_type TEXT NOT NULL CHECK (method_type IN ('card', 'bank_account', 'mobile_money')),
  last_four TEXT, -- Last 4 digits of card or account
  brand TEXT, -- Visa, Mastercard, MTN, etc.
  expiry_month INTEGER,
  expiry_year INTEGER,

  -- Status
  is_default BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,

  -- Regional Support
  country_code TEXT, -- GH, NG, KE, etc.
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),

  UNIQUE(provider, provider_payment_method_id)
);
```

### B.4 Legacy Subscription Schema (Deprecated)

#### Subscriptions Table
```sql
-- Subscription management with regional pricing
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Subscription Details
  tier TEXT NOT NULL CHECK (tier IN ('free', 'professional', 'team')),
  status TEXT NOT NULL CHECK (status IN ('active', 'cancelled', 'past_due', 'trialing', 'incomplete')),

  -- Pricing & Billing (African Market Optimized)
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),
  amount_cents INTEGER NOT NULL, -- Amount in smallest currency unit
  billing_cycle TEXT NOT NULL CHECK (billing_cycle IN ('monthly', 'annual')),

  -- Payment Provider Integration (Priority: Paystack > Flutterwave > Stripe)
  provider TEXT NOT NULL CHECK (provider IN ('paystack', 'flutterwave', 'stripe')),
  provider_subscription_id TEXT NOT NULL,
  provider_customer_id TEXT NOT NULL,
  provider_priority INTEGER DEFAULT 1, -- 1=Paystack, 2=Flutterwave, 3=Stripe

  -- Billing Periods
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  trial_end TIMESTAMPTZ,

  -- Discounts
  discount_percent INTEGER DEFAULT 0 CHECK (discount_percent >= 0 AND discount_percent <= 100),
  discount_type TEXT CHECK (discount_type IN ('onboarding', 'annual', 'affiliate')),

  UNIQUE(provider, provider_subscription_id)
);

-- Payment History
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),

  -- Payment Details
  amount_cents INTEGER NOT NULL,
  currency TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'succeeded', 'failed', 'refunded')),

  -- Provider Information
  provider TEXT NOT NULL,
  provider_payment_id TEXT NOT NULL,
  provider_charge_id TEXT,

  -- Metadata
  description TEXT,
  failure_reason TEXT,
  receipt_url TEXT,

  UNIQUE(provider, provider_payment_id)
);

-- Affiliate Program (Matches Migration)
CREATE TABLE affiliates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),

  -- Affiliate Details
  referral_code TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),

  -- Commission Tracking
  total_referrals INTEGER DEFAULT 0,
  total_commission_cents INTEGER DEFAULT 0,
  pending_commission_cents INTEGER DEFAULT 0,
  paid_commission_cents INTEGER DEFAULT 0,

  -- Payment Information
  payment_method JSONB, -- Bank details, mobile money, etc.
  last_payout_at TIMESTAMPTZ
);

-- Affiliate Commissions (Wallet-Based Model)
CREATE TABLE affiliate_commissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  affiliate_id UUID REFERENCES affiliates(id) ON DELETE CASCADE,
  wallet_transaction_id UUID REFERENCES wallet_transactions(id) ON DELETE CASCADE,
  referred_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),

  -- Commission Details
  commission_cents INTEGER NOT NULL,
  commission_type TEXT NOT NULL CHECK (commission_type IN ('initial_load', 'recurring_load', 'volume_bonus', 'performance_bonus')),
  original_load_amount_cents INTEGER NOT NULL, -- Original wallet load amount that triggered commission
  load_number INTEGER DEFAULT 1, -- Track if this is 1st, 2nd, 3rd load for this user
  commission_rate DECIMAL(5,2) NOT NULL, -- Commission percentage (e.g., 10.00 = 10%)

  -- Status & Payment
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'cancelled')),
  paid_at TIMESTAMPTZ,
  payment_reference TEXT,
  payout_transaction_id UUID REFERENCES wallet_transactions(id) ON DELETE SET NULL,

  -- Performance Tracking
  is_performance_bonus BOOLEAN DEFAULT FALSE,
  performance_tier TEXT CHECK (performance_tier IN ('bronze', 'silver', 'gold', 'platinum'))
);
```

### B.4 Real-time Synchronization Architecture

#### Cross-Platform Sync System
```typescript
// Real-time synchronization manager
export class SyncManager {
  private supabase: SupabaseClient;
  private syncChannel: RealtimeChannel;

  constructor(userId: string) {
    this.supabase = createClient(/* config */);
    this.syncChannel = this.supabase.channel(`user-sync-${userId}`);
  }

  // Initialize real-time subscriptions
  async initializeSync(platform: 'web' | 'excel') {
    // Subscribe to user data changes
    this.syncChannel
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'users',
        filter: `id=eq.${this.userId}`
      }, (payload) => {
        this.handleUserUpdate(payload, platform);
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'conversations',
        filter: `user_id=eq.${this.userId}`
      }, (payload) => {
        this.handleConversationUpdate(payload, platform);
      })
      .subscribe();
  }

  // Handle cross-platform data synchronization
  private async handleUserUpdate(payload: any, currentPlatform: 'web' | 'excel') {
    if (payload.eventType === 'UPDATE') {
      // Sync user preferences across platforms
      const updatedData = payload.new;

      if (currentPlatform === 'excel') {
        // Update Excel add-in UI
        await this.updateExcelInterface(updatedData);
      } else {
        // Update web app UI
        await this.updateWebInterface(updatedData);
      }
    }
  }

  // Conflict resolution for simultaneous edits
  async resolveConflict(localData: any, remoteData: any): Promise<any> {
    // Last-write-wins strategy with user notification
    if (localData.updated_at > remoteData.updated_at) {
      return localData;
    } else {
      // Notify user of conflict and use remote data
      await this.notifyConflict(localData, remoteData);
      return remoteData;
    }
  }
}
```

---

## Phase C: Integration Patterns (Week 3)

### C.1 Excel Add-in Integration

#### Office.js Integration Layer
```typescript
// Excel-specific data access layer
export class ExcelDataManager {
  private trpc: TRPCClient;

  constructor() {
    this.trpc = createTRPCClient({
      url: process.env.NEXT_PUBLIC_API_URL,
      headers: () => ({
        authorization: `Bearer ${this.getAuthToken()}`
      })
    });
  }

  // Extract data from Excel for AI analysis
  async extractWorkbookData(): Promise<WorkbookData> {
    return await Excel.run(async (context) => {
      const worksheets = context.workbook.worksheets;
      worksheets.load('items/name');

      await context.sync();

      const data: WorkbookData = {
        workbookId: this.generateWorkbookId(),
        worksheets: []
      };

      for (const worksheet of worksheets.items) {
        const range = worksheet.getUsedRange();
        range.load('values, formulas, address');
        await context.sync();

        data.worksheets.push({
          name: worksheet.name,
          data: range.values,
          formulas: range.formulas,
          range: range.address
        });
      }

      return data;
    });
  }

  // Insert AI results back into Excel
  async insertAnalysisResults(results: AnalysisResult, targetRange?: string): Promise<void> {
    return await Excel.run(async (context) => {
      const worksheet = context.workbook.worksheets.getActiveWorksheet();

      if (results.type === 'table') {
        // Insert tabular data
        const range = worksheet.getRange(targetRange || 'A1');
        range.values = results.data;
        range.format.autofitColumns();
      } else if (results.type === 'chart') {
        // Insert chart
        const chart = worksheet.charts.add(
          results.chartType,
          worksheet.getRange(results.dataRange),
          'auto'
        );
        chart.title.text = results.title;
      }

      await context.sync();
    });
  }
}
```

### C.2 AI Agent Integration

#### Agno Framework Integration
```typescript
// AI agent orchestration with Agno (Excella Model Stack)
export class AIAgentManager {
  private agno: AgnoClient;
  private posthog: PostHogClient; // Analytics integration

  constructor() {
    this.agno = new AgnoClient({
      apiKey: process.env.AGNO_API_KEY,
      providers: ['google', 'anthropic', 'deepseek'], // Gemini, Claude, DeepSeek
      langfuse: {
        publicKey: process.env.LANGFUSE_PUBLIC_KEY,
        secretKey: process.env.LANGFUSE_SECRET_KEY
      }
    });

    this.posthog = new PostHogClient({
      apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY
    });
  }

  // Execute AI analysis with intelligent model routing
  async executeAnalysis(request: AnalysisRequest): Promise<AnalysisResult> {
    // Track analytics
    this.posthog.capture('analysis_started', {
      userId: request.userId,
      platform: request.platform,
      dataSource: request.dataSource
    });

    try {
      // Intelligent model selection based on query complexity
      const modelSelection = this.selectOptimalModel(request.query, request.complexity);

      // Data Analysis Agent (Primary: Gemini 2.5 Pro - 80%)
      const dataAgent = this.agno.createAgent({
        name: 'DataAnalyst',
        model: modelSelection.primary, // 'gemini-2.5-pro' for most queries (80%)
        tools: ['pandas', 'numpy', 'scipy'],
        instructions: 'Analyze data and provide statistical insights using African business context with native Excel integration'
      });

      // Visualization Agent (Secondary: Gemini 2.5 Pro for consistency)
      const vizAgent = this.agno.createAgent({
        name: 'Visualizer',
        model: 'gemini-2.5-pro', // Consistent model for visualization
        tools: ['matplotlib', 'plotly', 'seaborn'],
        instructions: 'Create appropriate visualizations optimized for Excel integration'
      });

      // Code Generation Agent (DeepSeek Coder - 15%)
      const codeAgent = this.agno.createAgent({
        name: 'CodeExecutor',
        model: 'deepseek-coder',
        tools: ['python', 'pandas', 'numpy'],
        instructions: 'Generate and execute Python code for data processing and Excel formulas'
      });

      // Determine sandbox execution method (Hybrid Pyodide + E2B)
      const sandboxMethod = this.selectSandboxMethod(request.data, request.complexity);

      // Execute analysis workflow with hybrid sandbox
      const analysisResult = await this.executeWithSandbox(
        dataAgent,
        request.query,
        { data: request.data, context: request.context },
        sandboxMethod
      );

      // Generate code if needed (DeepSeek Coder)
      let codeExecution = null;
      if (request.includeCode) {
        codeExecution = await this.executeWithSandbox(
          codeAgent,
          `Generate Python code for: ${request.query}`,
          { data: request.data },
          sandboxMethod
        );
      }

      // Generate visualization if needed
      let visualization = null;
      if (request.includeVisualization) {
        visualization = await vizAgent.execute(
          `Create a visualization for: ${analysisResult.summary}`,
          { data: analysisResult.processedData }
        );
      }

      // Track completion analytics
      this.posthog.capture('analysis_completed', {
        userId: request.userId,
        modelUsed: modelSelection.primary,
        sandboxMethod,
        tokensUsed: analysisResult.tokensUsed.total,
        cost: analysisResult.cost
      });

      return {
        analysis: analysisResult,
        codeExecution,
        visualization,
        metadata: {
          tokensUsed: analysisResult.tokensUsed.total,
          cost: analysisResult.cost,
          executionTime: analysisResult.executionTime,
          modelUsed: modelSelection.primary,
          sandboxMethod,
          agents: ['DataAnalyst', 'Visualizer', 'CodeExecutor'].filter(Boolean)
        }
      };

    } catch (error) {
      this.posthog.capture('analysis_error', {
        userId: request.userId,
        error: error.message,
        platform: request.platform
      });
      throw error;
    }
  }

  // Intelligent orchestrator model selection
  private selectOptimalModel(query: string, complexity: 'simple' | 'complex' | 'code'): { primary: string, fallback: string } {
    // Code generation and Excel formulas - delegate to specialist
    if (complexity === 'code' || query.includes('formula') || query.includes('function')) {
      return { primary: 'deepseek-coder', fallback: 'gemini-2.5-pro' };
    }
    // Advanced reasoning tasks - delegate to reasoning specialist
    else if (complexity === 'complex' && (query.includes('reasoning') || query.includes('logic'))) {
      return { primary: 'deepseek-r1-0528', fallback: 'gemini-2.5-pro' };
    }
    // Simple queries - delegate to cost-efficient specialist
    else if (complexity === 'simple') {
      return { primary: 'gemini-flash', fallback: 'gemini-2.5-pro' };
    }
    // Default: Main orchestrator handles complex analysis requiring full context
    else {
      return { primary: 'gemini-2.5-pro', fallback: 'gemini-flash' };
    }
  }

  // Hybrid sandbox selection (Pyodide vs E2B)
  private selectSandboxMethod(data: any, complexity: 'simple' | 'complex' | 'code'): 'pyodide' | 'e2b' | 'hybrid' {
    const dataSize = JSON.stringify(data).length;
    const BROWSER_LIMIT = 10 * 1024 * 1024; // 10MB limit for Pyodide

    if (complexity === 'complex' || dataSize > BROWSER_LIMIT) {
      return 'e2b'; // Use E2B for heavy computations
    } else {
      return 'pyodide'; // Use Pyodide for fast, local execution
    }
  }

  // Execute with hybrid sandbox strategy
  private async executeWithSandbox(agent: any, query: string, data: any, method: string): Promise<any> {
    if (method === 'e2b') {
      // Server-side execution for complex workloads
      return await agent.execute(query, { ...data, sandbox: 'e2b' });
    } else {
      // Client-side execution for fast, offline capability
      return await agent.execute(query, { ...data, sandbox: 'pyodide' });
    }
  }
}
```

---

## Implementation Timeline

### Week 1: Core Data Models
- **Day 1-2**: User management and authentication schema
- **Day 3-4**: AI conversation and message schema
- **Day 5**: Database connectivity and data source schema

### Week 2: System Architecture
- **Day 1-2**: tRPC API router structure and core endpoints
- **Day 3-4**: Authentication flow and session management
- **Day 5**: Real-time synchronization and integration patterns

### Week 3: Advanced Features & Integration
- **Day 1-2**: Wallet-based billing and affiliate data models
- **Day 3-4**: Excel add-in and AI agent integration
- **Day 5**: Performance optimization and testing

### Week 4: Implementation & Testing
- **Day 1-2**: Database migration scripts and setup
- **Day 3-4**: API implementation and testing
- **Day 5**: End-to-end integration testing

---

## Success Criteria

### Data Model Validation
- [ ] Complete database schema with all required tables
- [ ] Row Level Security (RLS) policies implemented
- [ ] Data validation constraints and indexes
- [ ] Migration scripts tested and validated

### API Architecture
- [ ] Type-safe tRPC routers with full CRUD operations
- [ ] Authentication and authorization middleware
- [ ] Real-time synchronization between platforms
- [ ] Error handling and validation

### Integration Testing
- [ ] Excel add-in data extraction and insertion
- [ ] AI agent workflow execution
- [ ] Cross-platform session management
- [ ] Wallet-based billing and payment processing

---

## Risk Mitigation

### Technical Risks
1. **Database Performance**: Implement proper indexing and query optimization
2. **Real-time Sync Conflicts**: Use conflict resolution strategies and user notifications
3. **AI Cost Management**: Implement usage tracking and limits per tier
4. **Excel Integration Complexity**: Thorough testing with Office.js APIs

### Business Risks
1. **Payment Processing**: Multiple provider integration for reliability
2. **Data Security**: Encryption at rest and in transit
3. **Scalability**: Design for growth with proper caching and optimization
4. **Compliance**: GDPR and regional data protection requirements

---

## Next Steps

### Immediate Actions Required
1. **Review and approve** this comprehensive architecture plan
2. **Create Supabase project** and configure database schema
3. **Set up tRPC backend** with authentication middleware
4. **Implement core API endpoints** for user and conversation management
5. **Begin Excel add-in integration** with Office.js APIs

### Decision Points Needed
1. **Database encryption strategy** for sensitive connection data
2. **Real-time sync frequency** and conflict resolution approach
3. **Data retention policies** for conversations and usage analytics
4. **Magic UI animation preferences** for loading states and transitions
5. **E2B vs Pyodide routing thresholds** for optimal performance

### Follow-up Documentation
1. **Database Migration Scripts** - Detailed SQL scripts for Supabase setup
2. **API Documentation** - Complete tRPC endpoint specifications
3. **Integration Guides** - Step-by-step Excel and web app integration
4. **Security Implementation** - Authentication, encryption, and compliance measures

---

## ✅ **Architecture Corrections Applied**

### **AI Orchestrator Architecture Alignment - OPTIMIZED**
- ✅ **Orchestrator Model**: Gemini 2.5 Pro as main orchestrator - `gemini-2.5-pro` - Excel integration & context management
- ✅ **Coding Specialist**: DeepSeek Coder - `deepseek-coder` - 95% cost savings for coding tasks
- ✅ **Reasoning Specialist**: DeepSeek R1-0528 - `deepseek-r1-0528` - Advanced reasoning and complex logic
- ✅ **Quick Response Specialist**: Gemini Flash - `gemini-flash` - Cost-efficient operations
- ✅ **Intelligent Delegation**: Orchestrator-based routing where users don't choose models directly
- ✅ **Smart Routing**: Enhanced query-type based model selection for optimal cost/performance balance

### **Technology Stack Validation**
- ✅ **PostHog Integration**: Added proper analytics tracking instead of standalone Langfuse
- ✅ **Langfuse Integration**: Integrated through Agno framework as designed
- ✅ **Magic UI References**: Prepared for animation integration
- ✅ **Agno 1.2.2**: Proper configuration with multi-provider support

### **Pricing Model Corrections**
- ✅ **Query Limits**: Free tier 15 queries, Professional/Team unlimited
- ✅ **Tier Pricing**: $0 Free, $20 Professional, $18/user Team (min 5 users)
- ✅ **Usage Tracking**: Enhanced with model and sandbox method tracking

### **African Market Optimization**
- ✅ **Payment Priority**: Paystack (primary) > Flutterwave (secondary) > Stripe (backup)
- ✅ **Currency Support**: Added XOF, KES, UGX, TZS for broader African coverage
- ✅ **Regional Context**: African business context in AI instructions

### **Hybrid Sandbox Implementation**
- ✅ **Pyodide + E2B Strategy**: Intelligent routing based on data size and complexity
- ✅ **Execution Logic**: 10MB threshold for browser vs server execution
- ✅ **Offline Capability**: Pyodide for African connectivity optimization
- ✅ **Performance Optimization**: Smart sandbox selection for optimal user experience

### **Documentation Consistency**
- ✅ **Cross-Referenced**: All decisions align with technical stack and requirements
- ✅ **Research-Based**: Implementation follows established research findings
- ✅ **Excella-Specific**: Context reflects African market focus and business model

The architecture plan now fully aligns with Excella's established technical decisions and African market requirements.

Would you like me to proceed with creating any of these specific implementation files or dive deeper into any particular section?
