// Test file for ESLint and Prettier configuration
export interface User {
  id: string;
  name: string;
  email: string;
}

export const createUser = (name: string, email: string): User => {
  return {
    id: Math.random().toString(36),
    name,
    email,
  };
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};
