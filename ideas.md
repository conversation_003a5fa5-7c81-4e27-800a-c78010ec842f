1. when we are starting, we dont propose tech stack, rather functionalities that would be required. Then we will now run comprehensive rewsearch and do critical assessment of current available tools, software and frameworks that can help us perform those functionalities. 
3. Be gracious with the AI. Even you your context and memory has issues so lets do it one by one. Lets take one document build it, then AI references previous document for info to produce next etc.
4. UI/UX inspo fro Roo-Code, Augment Code, Cursor and Void and then Mobbin and Julius AI, Supabase and Postman.
5. With respect to data analysis, research and data science. what can AI agents do? Or how can AI or AI agents assist people who work in the financial, insurance, marketing etc fields who on a day-day use excel one way or another.

unambiguos comprehensive features or functionalities for excella

map how to achievefeartures to tech stack we have. explore relationship or interconnectivity between features and model same for tech stack 

data models

ui/ux flow



I need to validate why people should pay me $20 for an excel addin when they can just use chatgpt. Excella should really be worth more than $20 that would only be when people would want to purchase it for $20. Also I am thinking of <PERSON><PERSON><PERSON> increasing the team. what about connection to whatsApp and other social media channels and ability to analyze

lets check payment processing in webapp
tasks decomposer

[ ]Go over data-models-system architecure again, did we miss anything, is it reflective of the system we wanna build etc




[ ] We will connect notion for project-management

[ ] Gemini 2.5 pro is our base model for everything because its cost effiecinet and effective
but we would like to leverage claude 4 for certain implementatiosna dn sdeepcoder to . hope you get?

4. **Implementation Plan**: Only after the audit is complete and any gaps are addressed, create a detailed implementation plan that includes:
   - Development phases with specific deliverables
   - File structure and component organization
   - Dependencies and setup requirements
   - Testing strategy
   - Deployment considerations


you dont need to generate code right now, i just want the logic and visual flows to understand, i suffer from executive dysfucntion and memory issues so code isnt my thing. It means that Taskmaster should serve non-programmers with mental challenges like me who only need to provide highlevel decisions and overview. Now create a .md file for this(dont include code) just logic with appropriate flows and all. fortunately the taskmaster tool is open source and i have cloned it to my local folder, we will work together to enhance it