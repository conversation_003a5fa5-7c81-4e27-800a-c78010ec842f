# Excella Complete Design System
*Comprehensive UI/UX Design Documentation for AI-Powered Excel Add-in*

## Overview

This document consolidates all design system elements for Excella, an AI-powered Excel add-in with supporting web dashboard. The design system leverages React 19.0.0, Shadcn/ui, Magic UI, and Tailwind CSS to create a modern, accessible, and professional user experience optimized for Excel's taskpane environment and desktop/laptop usage.

## Design Philosophy

### Core Principles
1. **Excel-First Design**: Optimized for Excel taskpane environment and constraints
2. **Performance Optimized**: Lightweight components for smooth Excel integration
3. **Minimal & Clean**: Black/white design system inspired by Notion AI's subtle interface
4. **Conversational AI**: Natural language interaction with visual feedback
5. **Desktop-Focused**: Designed for desktop/laptop/tablet Excel users
6. **Subtle Interactions**: Minimal visual hierarchy with clean typography and spacing

### Technology Stack Integration
- **React 19.0.0**: Core framework with concurrent features
- **Shadcn/ui**: Foundational components (forms, tables, navigation)
- **Magic UI**: Animated components (loading states, feedback, transitions)
- **Tailwind CSS 3.4.x**: Utility-first styling with design tokens
- **Framer Motion**: Animation engine for smooth interactions
- **Next.js 15.x**: Web application framework with App Router

## Design Token System

### Color Palette
```css
/* Primary Colors - Black/White Minimal */
:root {
  --background: 0 0% 100%;           /* Pure white */
  --foreground: 0 0% 3.9%;           /* Near black */
  --card: 0 0% 100%;                 /* White cards */
  --card-foreground: 0 0% 3.9%;     /* Dark text on cards */
  --popover: 0 0% 100%;              /* White popovers */
  --popover-foreground: 0 0% 3.9%;  /* Dark text in popovers */
  
  /* Accent Colors - Subtle Grays */
  --primary: 0 0% 9%;                /* Dark gray primary */
  --primary-foreground: 0 0% 98%;    /* Light text on primary */
  --secondary: 0 0% 96.1%;           /* Light gray secondary */
  --secondary-foreground: 0 0% 9%;   /* Dark text on secondary */
  --muted: 0 0% 96.1%;               /* Muted background */
  --muted-foreground: 0 0% 45.1%;    /* Muted text */
  
  /* Functional Colors */
  --accent: 0 0% 96.1%;              /* Accent background */
  --accent-foreground: 0 0% 9%;      /* Accent text */
  --destructive: 0 84.2% 60.2%;      /* Error red */
  --destructive-foreground: 0 0% 98%; /* White text on error */
  --border: 0 0% 89.8%;              /* Light gray borders */
  --input: 0 0% 89.8%;               /* Input borders */
  --ring: 0 0% 3.9%;                 /* Focus rings */
  
  /* Suggested Action Colors */
  --blue-100: 219 95% 95%;           /* Ask question background */
  --blue-600: 221 83% 53%;           /* Ask question icon */
  --pink-100: 326 78% 95%;           /* Draft anything background */
  --pink-600: 330 81% 60%;           /* Draft anything icon */
  --green-100: 142 76% 94%;          /* Brainstorm background */
  --green-600: 142 71% 45%;          /* Brainstorm icon */
  --gray-100: 210 20% 95%;           /* Excel AI background */
  --gray-600: 215 16% 47%;           /* Excel AI icon */
}

/* Dark Mode */
.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
}
```

### Typography Scale
```css
/* Font System */
.font-system {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Typography Scale */
.text-xs { font-size: 0.75rem; line-height: 1rem; }      /* 12px */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }  /* 14px */
.text-base { font-size: 1rem; line-height: 1.5rem; }     /* 16px */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }  /* 18px */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }   /* 20px */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }      /* 24px */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* 30px */

/* Font Weights */
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
```

### Spacing System
```css
/* Spacing Scale (Tailwind CSS) */
.space-1 { margin: 0.25rem; }    /* 4px */
.space-2 { margin: 0.5rem; }     /* 8px */
.space-3 { margin: 0.75rem; }    /* 12px */
.space-4 { margin: 1rem; }       /* 16px */
.space-6 { margin: 1.5rem; }     /* 24px */
.space-8 { margin: 2rem; }       /* 32px */
.space-12 { margin: 3rem; }      /* 48px */
.space-16 { margin: 4rem; }      /* 64px */
```

## Component Architecture Strategy

### Integration Pattern: Shadcn/ui + Magic UI

#### Core Component Mapping
```typescript
// Foundation Layer (Shadcn/ui)
interface CoreComponents {
  // Layout & Structure
  Card: ShadcnCard;
  Sheet: ShadcnSheet;
  Dialog: ShadcnDialog;
  Tabs: ShadcnTabs;
  
  // Forms & Input
  Button: ShadcnButton;
  Input: ShadcnInput;
  Select: ShadcnSelect;
  Textarea: ShadcnTextarea;
  Form: ShadcnForm;
  
  // Data Display
  Table: ShadcnTable;
  Badge: ShadcnBadge;
  Avatar: ShadcnAvatar;
  Progress: ShadcnProgress;
  
  // Navigation
  NavigationMenu: ShadcnNavigationMenu;
  Breadcrumb: ShadcnBreadcrumb;
  Pagination: ShadcnPagination;
}

// Enhancement Layer (Magic UI)
interface EnhancedComponents {
  // Animations & Effects
  BlurFade: MagicBlurFade;
  NumberTicker: MagicNumberTicker;
  ShimmerButton: MagicShimmerButton;
  Ripple: MagicRipple;
  OrbitingCircles: MagicOrbitingCircles;
  
  // Loading States
  Meteors: MagicMeteors;
  AnimatedBeam: MagicAnimatedBeam;
  Confetti: MagicConfetti;
  
  // Interactive Elements
  ShineBorder: MagicShineBorder;
  GradientText: MagicGradientText;
  Particles: MagicParticles;
}
```

#### Component Enhancement Strategy
```typescript
// Enhanced Button Example
export function EnhancedButton({ children, variant = "default", ...props }: ButtonProps) {
  return (
    <ShineBorder> {/* Magic UI enhancement */}
      <Button variant={variant} {...props}> {/* Shadcn/ui base */}
        {children}
      </Button>
    </ShineBorder>
  );
}

// Enhanced Loading State
export function LoadingCard({ children }: { children: React.ReactNode }) {
  return (
    <Card className="relative overflow-hidden"> {/* Shadcn/ui base */}
      <Meteors number={20} /> {/* Magic UI enhancement */}
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}
```

## Excel Add-in Interface Design

### Finalized Welcome Interface Architecture

```
┌─────────────────────────────┐
│ 🖥️          + ⟲ ⚙ ✕      │ 60px
├─────────────────────────────┤
│                             │
│ Excella Avatar & Greeting   │ 80px
│ "Hi Alex, I'm Excella..."   │
│                             │
├─────────────────────────────┤
│                             │
│ Suggested Actions           │ 200px
│ • Ask a question            │
│ • Draft anything            │
│ • Brainstorm ideas          │
│ • What can Excel AI do?     │
│                             │
├─────────────────────────────┤
│                             │
│ Spacer                      │ Flexible
│                             │
├─────────────────────────────┤
│ Agent/Chat Toggle + Input   │ 80px
│ (Text + Microphone)         │
├─────────────────────────────┤
│ Powered by Fluxitude        │ 32px
└─────────────────────────────┘
```

### Key Implemented Features ✅

#### ✅ Server Connection Status
- Server icon with color-coded status (green blink = connected, red = disconnected)
- Indicates real-time AI service connection status

#### ✅ Personalized Avatar & Greeting
- Circular gradient avatar with "E" for Excella
- Dynamic greeting: "Hi [UserName], I'm Excella. How can I help you today?"

#### ✅ Four Suggested Actions with Colored Icons
- **Ask a question** (Blue question mark icon)
- **Draft anything** (Pink edit icon)  
- **Brainstorm ideas** (Green lightbulb icon)
- **What can Excella do?** (Gray play icon)

#### ✅ Agent/Chat Mode Toggle
- Dropdown selector in input area
- Switches between "Agent" and "Chat" modes
- Enables different interaction patterns

#### ✅ Integrated Voice Input
- Microphone icon positioned inside text input field
- Subtle placement for easy access without clutter

#### ✅ Fluxitude Branding
- "Powered by Fluxitude" footer text
- Establishes parent company relationship

#### ✅ Header Controls
- Plus, History, Settings, and Close buttons (right-aligned)
- Clean icon-based navigation with proper spacing

### Implemented Interface Components

#### Header Section (Production)
```typescript
interface HeaderProps {
  isConnected: boolean;
  onPlusClick: () => void;
  onHistoryClick: () => void;
  onSettingsClick: () => void;
  onCloseClick: () => void;
}

// Implemented header with server status and right-aligned controls
<div className="flex items-center justify-between p-4 border-b border-border">
  <div className="flex items-center gap-2">
    <Server
      className={`h-4 w-4 ${isConnected ? 'text-green-500 animate-pulse' : 'text-red-500'}`}
    />
  </div>
  <div className="flex items-center gap-1">
    <Button size="icon" variant="ghost" className="h-6 w-6" onClick={onPlusClick}>
      <Plus className="h-3 w-3" />
    </Button>
    <Button size="icon" variant="ghost" className="h-6 w-6" onClick={onHistoryClick}>
      <History className="h-3 w-3" />
    </Button>
    <Button size="icon" variant="ghost" className="h-6 w-6" onClick={onSettingsClick}>
      <Settings className="h-3 w-3" />
    </Button>
    <Button size="icon" variant="ghost" className="h-6 w-6" onClick={onCloseClick}>
      <X className="h-3 w-3" />
    </Button>
  </div>
</div>
```

#### Greeting Section (Production)
```typescript
interface GreetingProps {
  userName: string;
  avatar?: React.ReactNode;
}

// Implemented personalized greeting with avatar
<div className="p-6 space-y-4">
  <div className="flex items-center gap-3">
    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
      <span className="text-white font-semibold text-sm">E</span>
    </div>
    <div>
      <h2 className="text-lg font-semibold text-foreground">
        Hi {userName}, I'm Excella. How can I help you today?
      </h2>
    </div>
  </div>
</div>
```

#### Implemented Suggested Actions (Production)
```typescript
interface SuggestedAction {
  id: string;
  title: string;
  icon: React.ReactNode;
  color: string;
}

// Implemented 4 suggested actions with colored icons
<div className="p-4 space-y-3">
  <h3 className="text-sm font-medium text-muted-foreground">Suggested</h3>
  <div className="space-y-2">
    <Button variant="ghost" className="w-full justify-start h-auto p-3">
      <div className="flex items-center gap-3">
        <div className="w-5 h-5 rounded bg-blue-100 flex items-center justify-center">
          <HelpCircle className="h-3 w-3 text-blue-600" />
        </div>
        <span className="text-sm">Ask a question</span>
      </div>
    </Button>
    <Button variant="ghost" className="w-full justify-start h-auto p-3">
      <div className="flex items-center gap-3">
        <div className="w-5 h-5 rounded bg-pink-100 flex items-center justify-center">
          <Edit className="h-3 w-3 text-pink-600" />
        </div>
        <span className="text-sm">Draft anything</span>
      </div>
    </Button>
    <Button variant="ghost" className="w-full justify-start h-auto p-3">
      <div className="flex items-center gap-3">
        <div className="w-5 h-5 rounded bg-green-100 flex items-center justify-center">
          <Lightbulb className="h-3 w-3 text-green-600" />
        </div>
        <span className="text-sm">Brainstorm ideas</span>
      </div>
    </Button>
    <Button variant="ghost" className="w-full justify-start h-auto p-3">
      <div className="flex items-center gap-3">
        <div className="w-5 h-5 rounded bg-gray-100 flex items-center justify-center">
          <Play className="h-3 w-3 text-gray-600" />
        </div>
        <span className="text-sm">What can Excel AI do?</span>
      </div>
    </Button>
  </div>
</div>
```

#### Implemented Input Area (Production)
```typescript
interface InputAreaProps {
  onSendMessage: (message: string) => void;
  agentMode: 'agent' | 'chat';
  onModeChange: (mode: 'agent' | 'chat') => void;
}

// Implemented input with Agent/Chat toggle and microphone
<div className="p-4 border-t border-border">
  <div className="flex items-center gap-2">
    <Select value={agentMode} onValueChange={onModeChange}>
      <SelectTrigger className="w-20">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="agent">Agent</SelectItem>
        <SelectItem value="chat">Chat</SelectItem>
      </SelectContent>
    </Select>
    <div className="flex-1 relative">
      <Input
        placeholder="Ask anything or select..."
        className="pr-10 border-border"
      />
      <Button size="icon" variant="ghost" className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6">
        <Mic className="h-3 w-3" />
      </Button>
    </div>
  </div>
  <div className="flex items-center justify-center gap-1 mt-2">
    <span className="text-xs text-muted-foreground">Powered by Fluxitude</span>
  </div>
</div>
```

## Conversational AI Interface Patterns

### Message Type System
```typescript
// types/conversation.ts
export interface BaseMessage {
  id: string;
  timestamp: Date;
  type: MessageType;
  status: MessageStatus;
  metadata?: MessageMetadata;
}

export type MessageType =
  | 'user-text'
  | 'user-voice'
  | 'assistant-text'
  | 'assistant-analysis'
  | 'assistant-visualization'
  | 'assistant-code'
  | 'system-notification'
  | 'error';

export type MessageStatus =
  | 'sending'
  | 'sent'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface MessageMetadata {
  executionTime?: number;
  tokensUsed?: number;
  confidence?: number;
  dataSource?: string;
  codeLanguage?: 'python' | 'javascript' | 'sql';
  chartType?: 'bar' | 'line' | 'pie' | 'scatter' | 'heatmap';
  cellReferences?: string[];
}
```

### Chat Message Components
```typescript
// components/conversation/message.tsx
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Copy, ThumbsUp, ThumbsDown, RotateCcw } from 'lucide-react';
import { BlurFade } from '@/components/magicui/blur-fade';
import { NumberTicker } from '@/components/magicui/number-ticker';

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'processing' | 'completed' | 'error';
  metadata?: {
    executionTime?: number;
    tokensUsed?: number;
    confidence?: number;
  };
}

export function ChatMessage({ message, index }: { message: Message; index: number }) {
  const isUser = message.type === 'user';
  const isSystem = message.type === 'system';

  return (
    <BlurFade delay={index * 0.1} inView>
      <div className={`flex gap-3 p-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        {!isSystem && (
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback className={isUser ? 'bg-primary text-primary-foreground' : 'bg-accent text-accent-foreground'}>
              {isUser ? 'U' : 'E'}
            </AvatarFallback>
          </Avatar>
        )}

        {/* Message Content */}
        <div className={`flex-1 space-y-2 ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>
          {/* Message Bubble */}
          <Card className={`max-w-[85%] ${isUser ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
            <CardContent className="p-3">
              <div className="text-sm whitespace-pre-wrap">{message.content}</div>

              {/* Metadata */}
              {message.metadata && !isUser && (
                <div className="flex items-center gap-2 mt-2 pt-2 border-t border-border/50">
                  {message.metadata.executionTime && (
                    <Badge variant="secondary" className="text-xs">
                      <NumberTicker value={message.metadata.executionTime} />ms
                    </Badge>
                  )}
                  {message.metadata.confidence && (
                    <Badge variant="secondary" className="text-xs">
                      {Math.round(message.metadata.confidence * 100)}% confident
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Message Actions */}
          {!isUser && message.status === 'completed' && (
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Copy className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <ThumbsUp className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <ThumbsDown className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <RotateCcw className="h-3 w-3" />
              </Button>
            </div>
          )}

          {/* Timestamp */}
          <span className="text-xs text-muted-foreground">
            {message.timestamp.toLocaleTimeString()}
          </span>
        </div>
      </div>
    </BlurFade>
  );
}
```

### Loading States with Magic UI
```typescript
// components/conversation/loading-message.tsx
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Meteors } from '@/components/magicui/meteors';

export function LoadingMessage({ stage }: { stage: 'thinking' | 'processing' | 'generating' }) {
  const getLoadingText = () => {
    switch (stage) {
      case 'thinking': return 'Analyzing your request...';
      case 'processing': return 'Processing data...';
      case 'generating': return 'Generating response...';
      default: return 'Working...';
    }
  };

  return (
    <div className="flex gap-3 p-3">
      <Avatar className="h-8 w-8 shrink-0">
        <AvatarFallback className="bg-accent text-accent-foreground">E</AvatarFallback>
      </Avatar>

      <Card className="flex-1 relative overflow-hidden">
        <Meteors number={20} />
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
            </div>
            <span className="text-sm text-muted-foreground">{getLoadingText()}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

### Voice Input Interface
```typescript
// components/conversation/voice-interface.tsx
import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Languages,
  Waveform
} from 'lucide-react';
import { Ripple } from '@/components/magicui/ripple';
import { OrbitingCircles } from '@/components/magicui/orbiting-circles';

interface VoiceInterfaceProps {
  onTranscription: (text: string) => void;
  language: 'en' | 'fr';
  onLanguageChange: (lang: 'en' | 'fr') => void;
}

export function VoiceInterface({
  onTranscription,
  language,
  onLanguageChange
}: VoiceInterfaceProps) {
  const [isListening, setIsListening] = useState(false);
  const [transcriptionText, setTranscriptionText] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    setIsSupported('webkitSpeechRecognition' in window || 'SpeechRecognition' in window);
  }, []);

  const toggleListening = () => {
    setIsListening(!isListening);
    if (!isListening) {
      startListening();
    } else {
      stopListening();
    }
  };

  const startListening = () => {
    // Voice recognition implementation
  };

  const stopListening = () => {
    // Stop voice recognition
  };

  if (!isSupported) {
    return (
      <Card className="p-4">
        <CardContent>
          <Badge variant="secondary" className="text-xs">
            Voice input not supported in this browser
          </Badge>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative">
      <CardContent className="p-4">
        {/* Voice Control Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Mic className="h-4 w-4" />
            <span className="text-sm font-medium">Voice Input</span>
          </div>

          {/* Language Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onLanguageChange(language === 'en' ? 'fr' : 'en')}
            className="h-6 px-2"
          >
            <Languages className="h-3 w-3 mr-1" />
            {language.toUpperCase()}
          </Button>
        </div>

        {/* Voice Input Button */}
        <div className="flex justify-center mb-4">
          <div className="relative">
            <Button
              variant={isListening ? "destructive" : "outline"}
              size="lg"
              onClick={toggleListening}
              className="relative h-16 w-16 rounded-full"
            >
              {isListening ? (
                <>
                  <MicOff className="h-6 w-6" />
                  <Ripple />
                </>
              ) : (
                <Mic className="h-6 w-6" />
              )}
            </Button>

            {/* Orbiting circles for active listening */}
            {isListening && (
              <div className="absolute inset-0 pointer-events-none">
                <OrbitingCircles
                  className="size-[80px] border-none bg-transparent"
                  duration={20}
                  delay={20}
                  radius={40}
                >
                  <div className="h-2 w-2 rounded-full bg-primary/60" />
                </OrbitingCircles>
                <OrbitingCircles
                  className="size-[80px] border-none bg-transparent"
                  duration={20}
                  delay={10}
                  radius={40}
                  reverse
                >
                  <div className="h-1 w-1 rounded-full bg-accent/60" />
                </OrbitingCircles>
              </div>
            )}
          </div>
        </div>

        {/* Audio Level Indicator */}
        {isListening && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Waveform className="h-4 w-4" />
              <span className="text-sm">Audio Level</span>
            </div>
            <Progress value={audioLevel} className="h-2" />
          </div>
        )}

        {/* Live transcription */}
        {transcriptionText && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Live Transcription</span>
              <Badge variant="secondary" className="text-xs">
                {Math.round(confidence * 100)}% confident
              </Badge>
            </div>
            <p className="text-sm italic">{transcriptionText}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

## Web Dashboard Design

### Navigation & Layout Structure
```typescript
// components/web/dashboard-layout.tsx
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  BarChart3,
  Settings,
  HelpCircle,
  User,
  CreditCard,
  FileSpreadsheet,
  Users,
  Calendar
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  user: {
    name: string;
    email: string;
    plan: 'free' | 'professional' | 'team';
    avatar?: string;
  };
}

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const getPlanBadgeVariant = (plan: string) => {
    switch (plan) {
      case 'professional': return 'default';
      case 'team': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between px-4">
          {/* Left section */}
          <div className="flex items-center gap-4">
            {/* Brand */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                <span className="text-white font-bold text-sm">E</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-lg font-semibold">Excella</h1>
                <Badge variant={getPlanBadgeVariant(user.plan)} className="text-xs">
                  {user.plan.charAt(0).toUpperCase() + user.plan.slice(1)}
                </Badge>
              </div>
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm">
              <HelpCircle className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {children}
      </div>
    </div>
  );
}
```

### Dashboard Overview Component
```typescript
// components/web/dashboard-overview.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  FileSpreadsheet,
  Zap
} from 'lucide-react';
import { BlurFade } from '@/components/magicui/blur-fade';
import { NumberTicker } from '@/components/magicui/number-ticker';

interface DashboardOverviewProps {
  user: {
    name: string;
    plan: 'free' | 'professional' | 'team';
    queriesUsed: number;
    queryLimit: number;
    analysesThisMonth: number;
    chartsCreated: number;
  };
}

export function DashboardOverview({ user }: DashboardOverviewProps) {
  const usagePercentage = (user.queriesUsed / user.queryLimit) * 100;

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <BlurFade delay={0.1} inView>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Welcome back, {user.name}!</h1>
            <p className="text-muted-foreground">
              Here's what's happening with your Excel AI assistant.
            </p>
          </div>
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            View Reports
          </Button>
        </div>
      </BlurFade>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <BlurFade delay={0.2} inView>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Queries Used</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <NumberTicker value={user.queriesUsed} />
              </div>
              <div className="mt-2">
                <Progress value={usagePercentage} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {user.queryLimit - user.queriesUsed} remaining
                </p>
              </div>
            </CardContent>
          </Card>
        </BlurFade>

        <BlurFade delay={0.3} inView>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Analyses</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <NumberTicker value={user.analysesThisMonth} />
              </div>
              <p className="text-xs text-muted-foreground">
                +12% from last month
              </p>
            </CardContent>
          </Card>
        </BlurFade>

        <BlurFade delay={0.4} inView>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Charts Created</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <NumberTicker value={user.chartsCreated} />
              </div>
              <p className="text-xs text-muted-foreground">
                +8% from last month
              </p>
            </CardContent>
          </Card>
        </BlurFade>

        <BlurFade delay={0.5} inView>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Plan Status</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold capitalize">{user.plan}</div>
              <p className="text-xs text-muted-foreground">
                {user.plan === 'free' ? 'Upgrade for more features' : 'Active subscription'}
              </p>
            </CardContent>
          </Card>
        </BlurFade>
      </div>

      {/* Recent Activity */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest Excel AI interactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Activity items would go here */}
              <div className="flex items-center gap-4">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Sales data analysis completed</p>
                  <p className="text-xs text-muted-foreground">2 minutes ago</p>
                </div>
                <Badge variant="secondary">Analysis</Badge>
              </div>
              <div className="flex items-center gap-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Chart created: Revenue trends</p>
                  <p className="text-xs text-muted-foreground">15 minutes ago</p>
                </div>
                <Badge variant="secondary">Visualization</Badge>
              </div>
              <div className="flex items-center gap-4">
                <div className="w-2 h-2 bg-purple-500 rounded-full" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Database connected: PostgreSQL</p>
                  <p className="text-xs text-muted-foreground">1 hour ago</p>
                </div>
                <Badge variant="secondary">Connection</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>
    </div>
  );
}
```

## Implementation Guidelines

### Development Workflow
```typescript
// Component development pattern
// 1. Start with Shadcn/ui base component
// 2. Add Magic UI enhancements for animations
// 3. Apply Excella design tokens
// 4. Test in Excel taskpane environment

// Example: Enhanced Button Component
import { Button as ShadcnButton } from '@/components/ui/button';
import { ShimmerButton } from '@/components/magicui/shimmer-button';
import { cn } from '@/lib/utils';

interface ExcellaButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  shimmer?: boolean;
  children: React.ReactNode;
}

export function ExcellaButton({
  variant = 'default',
  size = 'default',
  shimmer = false,
  className,
  children,
  ...props
}: ExcellaButtonProps) {
  if (shimmer) {
    return (
      <ShimmerButton className={cn('excella-button', className)} {...props}>
        {children}
      </ShimmerButton>
    );
  }

  return (
    <ShadcnButton
      variant={variant}
      size={size}
      className={cn('excella-button', className)}
      {...props}
    >
      {children}
    </ShadcnButton>
  );
}
```

### Excel Add-in Specific Considerations
```typescript
// Excel taskpane constraints and optimizations
export const EXCEL_CONSTRAINTS = {
  // Taskpane dimensions
  MIN_WIDTH: 320,
  MAX_WIDTH: 400,
  MIN_HEIGHT: 500,

  // Performance limits
  MAX_CONCURRENT_REQUESTS: 3,
  DEBOUNCE_DELAY: 300,

  // Office.js integration
  OFFICE_API_VERSION: '1.17',
  SUPPORTED_HOSTS: ['Excel'],

  // Memory considerations
  MAX_CHART_DATA_POINTS: 10000,
  MAX_TABLE_ROWS: 5000,
} as const;

// Excel-specific component wrapper
export function ExcelTaskpaneWrapper({ children }: { children: React.ReactNode }) {
  const [isExcelReady, setIsExcelReady] = useState(false);
  const [taskpaneWidth, setTaskpaneWidth] = useState(EXCEL_CONSTRAINTS.MIN_WIDTH);

  useEffect(() => {
    // Initialize Office.js
    Office.onReady((info) => {
      if (info.host === Office.HostType.Excel) {
        setIsExcelReady(true);
        // Set up taskpane resize listener
        Office.ribbon.requestUpdate([]);
      }
    });
  }, []);

  if (!isExcelReady) {
    return <LoadingSpinner />;
  }

  return (
    <div
      className="excel-taskpane"
      style={{
        width: `${taskpaneWidth}px`,
        minHeight: `${EXCEL_CONSTRAINTS.MIN_HEIGHT}px`
      }}
    >
      {children}
    </div>
  );
}
```

### Performance Optimization
```typescript
// Lazy loading for Excel add-in
import { lazy, Suspense } from 'react';
import { LoadingMessage } from '@/components/conversation/loading-message';

// Lazy load heavy components
const VoiceInterface = lazy(() => import('@/components/conversation/voice-interface'));
const DatabaseConnector = lazy(() => import('@/components/data/database-connector'));
const ChartGenerator = lazy(() => import('@/components/visualization/chart-generator'));

// Performance-optimized component loading
export function LazyComponentLoader({
  component: Component,
  fallback
}: {
  component: React.LazyExoticComponent<any>;
  fallback?: React.ReactNode;
}) {
  return (
    <Suspense fallback={fallback || <LoadingMessage stage="thinking" />}>
      <Component />
    </Suspense>
  );
}

// Memory management for large datasets
export class DataManager {
  private cache = new Map<string, any>();
  private readonly MAX_CACHE_SIZE = 50;

  async processLargeDataset(data: any[], chunkSize: number = 1000) {
    const chunks = [];
    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push(data.slice(i, i + chunkSize));
    }

    // Process chunks with yield for UI responsiveness
    for (const chunk of chunks) {
      await new Promise(resolve => setTimeout(resolve, 0));
      // Process chunk
    }
  }
}
```

## Cross-Platform Integration

### Authentication & Session Management
```typescript
// Unified authentication across web and Excel
export class AuthenticationManager {
  private supabase: SupabaseClient;
  private tokenStorage: TokenStorage;

  async authenticateUser(platform: 'web' | 'excel'): Promise<AuthResult> {
    if (platform === 'web') {
      return this.webAuthentication();
    } else {
      return this.excelAuthentication();
    }
  }

  private async webAuthentication(): Promise<AuthResult> {
    // Full OAuth flow in web browser
    const { data, error } = await this.supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    });

    return { success: !error, data, error };
  }

  private async excelAuthentication(): Promise<AuthResult> {
    // Token-based authentication for Excel add-in
    const token = await this.tokenStorage.getToken();
    if (token && !this.isTokenExpired(token)) {
      return { success: true, data: { token } };
    }

    // Redirect to web for authentication
    return this.redirectToWebAuth();
  }
}
```

### Data Synchronization
```typescript
// Real-time data sync between platforms
export class DataSynchronizer {
  private supabase: SupabaseClient;
  private eventEmitter: EventEmitter;

  async syncUserData(userId: string): Promise<void> {
    // Subscribe to real-time changes
    const subscription = this.supabase
      .channel(`user-${userId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_data',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          this.eventEmitter.emit('data-updated', payload);
        }
      )
      .subscribe();

    return subscription;
  }

  async updateUserPreferences(preferences: UserPreferences): Promise<void> {
    const { error } = await this.supabase
      .from('user_preferences')
      .upsert(preferences);

    if (!error) {
      this.eventEmitter.emit('preferences-updated', preferences);
    }
  }
}
```

## African Market Adaptations

### Connectivity Optimization
```typescript
// Progressive loading and offline capabilities
export class ConnectivityManager {
  private isOnline: boolean = navigator.onLine;
  private connectionQuality: 'fast' | 'slow' | 'offline' = 'fast';

  constructor() {
    this.setupConnectivityMonitoring();
  }

  private setupConnectivityMonitoring(): void {
    // Monitor connection changes
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.assessConnectionQuality();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.connectionQuality = 'offline';
      this.enableOfflineMode();
    });

    // Assess initial connection quality
    this.assessConnectionQuality();
  }

  private async assessConnectionQuality(): Promise<void> {
    if (!this.isOnline) return;

    try {
      const startTime = performance.now();
      await fetch('/api/ping', { method: 'HEAD' });
      const endTime = performance.now();

      const latency = endTime - startTime;
      this.connectionQuality = latency > 1000 ? 'slow' : 'fast';

      this.adaptToConnectionQuality();
    } catch {
      this.connectionQuality = 'slow';
    }
  }

  private adaptToConnectionQuality(): void {
    switch (this.connectionQuality) {
      case 'fast':
        this.enableFullFeatures();
        break;
      case 'slow':
        this.enableLimitedFeatures();
        break;
      case 'offline':
        this.enableOfflineMode();
        break;
    }
  }

  private enableOfflineMode(): void {
    // Switch to Pyodide-only execution
    // Disable real-time features
    // Cache essential data locally
  }
}
```

### Localization & Cultural Adaptation
```typescript
// Multi-language support with cultural considerations
export const LOCALIZATION_CONFIG = {
  supportedLanguages: ['en', 'fr'],
  defaultLanguage: 'en',

  // Regional adaptations
  regions: {
    'west-africa': {
      currencies: ['XOF', 'GHS', 'NGN'],
      paymentMethods: ['paystack', 'flutterwave'],
      businessContext: 'sme-focused',
      supportChannels: ['whatsapp', 'email']
    },
    'east-africa': {
      currencies: ['KES', 'UGX', 'TZS'],
      paymentMethods: ['mpesa', 'airtel-money'],
      businessContext: 'sme-focused',
      supportChannels: ['whatsapp', 'sms']
    }
  }
} as const;

// Cultural adaptation component
export function CulturalAdaptationProvider({
  children,
  region
}: {
  children: React.ReactNode;
  region: keyof typeof LOCALIZATION_CONFIG.regions;
}) {
  const config = LOCALIZATION_CONFIG.regions[region];

  return (
    <CulturalContext.Provider value={config}>
      {children}
    </CulturalContext.Provider>
  );
}
```

## Accessibility & Compliance

### WCAG 2.1 Compliance
```typescript
// Accessibility utilities and components
export const ACCESSIBILITY_CONFIG = {
  // Color contrast ratios
  minContrastRatio: 4.5,
  largeTextContrastRatio: 3.0,

  // Focus management
  focusRingWidth: '2px',
  focusRingColor: 'hsl(var(--ring))',

  // Screen reader support
  announceChanges: true,
  liveRegionPoliteness: 'polite' as const,
} as const;

// Accessible component wrapper
export function AccessibleWrapper({
  children,
  label,
  description,
  role
}: {
  children: React.ReactNode;
  label?: string;
  description?: string;
  role?: string;
}) {
  const announceRef = useRef<HTMLDivElement>(null);

  return (
    <div
      role={role}
      aria-label={label}
      aria-describedby={description ? 'description' : undefined}
    >
      {children}
      {description && (
        <div id="description" className="sr-only">
          {description}
        </div>
      )}
      <div
        ref={announceRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />
    </div>
  );
}
```

---

*This complete design system provides the foundation for building consistent, accessible, and performant user interfaces across the Excella platform, with specific optimizations for Excel integration and African market requirements.*
