# Documentation Guide

## 📁 Documentation Organization

This project uses a dual documentation structure to separate general documentation from Excella-specific content.

## 🎯 Excella Project Documentation

**Location**: **`.excella/`** directory
**Purpose**: Excella-specific project content, research, and specifications

### Quick Access

- **📖 Documentation Index**: [`.excella/readme.md`](.excella/readme.md)
- **🚀 Getting Started**: [`.excella/setup/package-installation-guide.md`](.excella/setup/package-installation-guide.md)
- **🏗️ Technical Stack**: [`.excella/core/technical-stack.md`](.excella/core/technical-stack.md)
- **📋 Project Brief**: [`.excella/core/project-brief.md`](.excella/core/project-brief.md)
- **📝 Requirements**: [`.excella/core/requirements.md`](.excella/core/requirements.md)
- **🔬 Research Findings**: [`.excella/research/`](.excella/research/)

## 📚 General Documentation

**Location**: **`docs/`** directory  
**Purpose**: General development documentation, team processes, and non-project-specific content

- **📖 General Docs Index**: [`docs/readme.md`](docs/readme.md)
- **🤝 Contributing Guidelines**: [`docs/contributing.md`](docs/contributing.md)
- **📜 Code of Conduct**: `docs/code-of-conduct.md` (future)
- **⚖️ License Information**: `docs/license.md` (future)

## 🗂️ Complete Directory Structure

```
project-root/
├── docs/                        # General documentation
│   ├── readme.md               # General docs guide
│   ├── contributing.md         # Contributing guidelines
│   └── ...                     # Team processes, contributing guides, etc.
└── .excella/                   # Excella-specific documentation
    ├── research-plan.md        # Overall research strategy and planning
    ├── core/                   # Core documentation, specs, and architecture
    ├── research/               # Research phases and analysis documents
    ├── setup/                  # Installation and configuration guides
    └── assets/                 # Project-specific assets and resources
```

## 🎯 When to Use Which Directory

### Use `docs/` for:
- General development practices and workflows
- Team processes and procedures
- Contributing guidelines and code of conduct
- Generic technical guides
- Open source project documentation

### Use `.excella/` for:
- Excella project-specific content
- Technical stack decisions and research
- Project requirements and specifications
- Installation and setup guides
- Research findings and analysis

## 📝 File Naming Convention

This project uses **kebab-case** for all file names:
- ✅ `readme.md`
- ✅ `contributing.md`
- ✅ `technical-stack.md`
- ✅ `package-installation-guide.md`
- ❌ `README.md`
- ❌ `CONTRIBUTING.md`

---

*This dual structure provides clear separation between general documentation and Excella-specific project content.*
