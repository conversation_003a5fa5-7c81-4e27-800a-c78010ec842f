# Requirements Specification: Excella
*Version 2.0 | December 2024 - Solo Developer + AI-Assisted Development*

## Document Overview

This document provides formal requirements for the Excella project, revised for solo developer + AI-assisted development reality. Requirements are categorized by type and priority using the MoSCoW method, with realistic scope for bootstrap development approach.

**Target Market Update**: As of December 2024, students and researchers are no longer target user segments. Focus is on business professionals, SME owners, and team administrators in African markets, delivered through a globally competitive product that maintains world-class standards.

**Requirement ID Format**: `REQ-[Category]-[Number]`
- **BUS**: Business Requirements
- **FUN**: Functional Requirements  
- **NFR**: Non-Functional Requirements
- **TEC**: Technical Requirements
- **USR**: User Requirements

---

## 1. Business Requirements

### 1.1 Project Objectives

#### REQ-BUS-001: Market Democratization
- **Priority**: Must Have
- **Description**: Enable non-technical users to perform advanced analytics through natural language and voice commands
- **Success Criteria**: 90% user satisfaction rate
- **Acceptance Criteria**:
  - Users can perform complex data analysis without programming knowledge
  - Natural language queries produce accurate results
  - Voice commands work reliably in English and French

#### REQ-BUS-002: Product-Market Fit Validation
- **Priority**: Must Have
- **Description**: Validate product-market fit with 100-500 active users within 6 months
- **Success Criteria**: 70%+ user retention after 30 days, 4.0+ star rating
- **Acceptance Criteria**:
  - 100-500 active users providing regular feedback
  - 50+ detailed user interviews conducted
  - Measurable user engagement and retention metrics
  - Positive user testimonials and case studies

#### REQ-BUS-003: Revenue Validation
- **Priority**: Must Have
- **Description**: Achieve $90K-1.8M ARR within 12 months through wallet-based pay-per-use model
- **Success Criteria**: $90K-1.8M Annual Recurring Revenue (Conservative: $90K, Realistic: $675K, Optimistic: $1.8M - validated June 2025 financial model)
- **Acceptance Criteria**:
  - Functional wallet-based billing system with pay-per-use pricing strategy
  - **Minimum Wallet Load**: $5 for all users (individual and enterprise) - universal accessibility
  - **Cost Per Query**: $0.02-0.20 based on complexity (Simple: $0.02-0.04, Advanced: $0.12-0.20, Average: $0.09)
  - **AI Model Costs**: Gemini 2.5 Pro ($1.25/$10.00 per 1M tokens), DeepSeek Coder ($0.27/$1.10), DeepSeek R1 ($0.55/$2.19), Gemini Flash ($0.15/$0.60)
  - **Gross Margin**: 32% achieved through 20-33% markup on all queries (validated financial analysis)
  - **Optional Transparency**: Users can toggle cost visibility and detailed token breakdowns
  - **Auto-Refill**: Automatic wallet replenishment when balance drops below $2.00 threshold
  - Payment processing optimized for African markets (Paystack Ghana 1.95%, Flutterwave Nigeria 2.0% local, 4.8% international) with global payment backup (Stripe, PayPal)
  - 1,000-10,000 active users with average spend of $7.50-15.00/month
  - Unit economics: 32% gross margins with real-time cost calculation (validated financial analysis)

#### REQ-BUS-004: Community Building
- **Priority**: Should Have
- **Description**: Build engaged user community of 100-300 members
- **Success Criteria**: 100-300 active community members
- **Acceptance Criteria**:
  - WhatsApp groups with regular engagement and voice communication
  - 20+ tutorial videos and comprehensive documentation
  - 5+ detailed case studies from African business users showcasing global-quality results
  - Regular user feedback sessions and feature requests via WhatsApp voice calls

### 1.2 Constraints

#### REQ-BUS-005: Bootstrap Budget Constraint
- **Priority**: Must Have
- **Description**: MVP development within validated operational budget (June 2025 financial analysis)
- **Acceptance Criteria**:
  - Total Year 1 operational costs: $9.5K-17.9K annually (infrastructure $600, AI APIs $8,400-16,800, tools $500) - validated financial analysis
  - Break-even achieved with 67+ active users (Month 1-2) - validated financial analysis
  - Optimized AI model stack: Gemini 2.5 Pro (orchestrator), DeepSeek Coder, DeepSeek R1, Gemini Flash with intelligent routing
  - No external funding required for MVP validation

#### REQ-BUS-006: Solo Development Timeline
- **Priority**: Must Have
- **Description**: 6-month MVP delivery using AI-assisted development
- **Acceptance Criteria**:
  - Functional Excel add-in with AI capabilities delivered within 6 months
  - Solo developer using AI coding assistants (Claude, GitHub Copilot, etc.)
  - Iterative development with user feedback integration

#### REQ-BUS-007: Solo Developer Constraint
- **Priority**: Must Have
- **Description**: Single developer using AI-assisted coding tools
- **Acceptance Criteria**:
  - One primary developer leveraging AI coding assistants
  - AI tools for code generation, debugging, and optimization
  - Community support and user feedback for validation

---

## 2. Functional Requirements

### 2.1 Excel Add-in Core Features

#### REQ-FUN-001: Conversational Interface ✅ IMPLEMENTED
- **Priority**: Must Have
- **Description**: Sidebar chat/voice assistant using React 19.0.0 and Shadcn/ui components
- **Acceptance Criteria**:
  - ✅ Chat interface integrated in Excel sidebar using Office.js ExcelApi 1.17+
  - ✅ Voice input processing via microphone icon in text input field
  - ✅ Agent/Chat mode toggle for different interaction patterns
  - ✅ Personalized greeting with Excella avatar and user name
  - ✅ Four suggested actions with colored icons for quick access
  - ✅ Live status indicator showing real-time connection
  - ✅ Fluxitude branding in footer
  - [ ] Multi-turn conversation support via Agno 1.2.2 framework
  - [ ] Context awareness across sessions with conversation memory

#### REQ-FUN-002: Multilingual Support
- **Priority**: Must Have
- **Description**: Context-aware conversations in English and French using next-intl
- **Acceptance Criteria**:
  - 95% accuracy in voice recognition via OpenAI Whisper API
  - Automatic language detection through AI models
  - Seamless language switching with next-intl for Next.js 15
  - Cultural context adaptation via GPT-4, Claude, Gemini models

#### REQ-FUN-003: Code Execution Sandbox
- **Priority**: Must Have
- **Description**: Hybrid Pyodide (WebAssembly) + E2B Code Interpreter execution
- **Acceptance Criteria**:
  - Client-side Python execution via Pyodide WebAssembly
  - Server-side execution via E2B Code Interpreter for complex operations
  - Automatic fallback between execution environments
  - Security isolation and resource limits

#### REQ-FUN-004: Data Analysis Engine
- **Priority**: Must Have
- **Description**: Statistical analysis using pandas 2.2.3, NumPy 2.2.0, and AI models
- **Acceptance Criteria**:
  - Statistical analysis functions via pandas and scipy
  - Automated outlier detection using scikit-learn
  - Pattern identification through AI models and data libraries
  - Hypothesis testing capabilities with statistical explanations

#### REQ-FUN-005: Visualization Generation
- **Priority**: Must Have
- **Description**: Chart generation using matplotlib 3.10.0 and plotly 6.1.2
- **Acceptance Criteria**:
  - Automatic chart type selection based on data analysis
  - Interactive dashboard creation with Shadcn/ui components
  - Direct embedding in Excel sheets via Office.js APIs
  - Export capabilities using plotly integration

### 2.2 Web Application Features

#### REQ-FUN-006: User Management
- **Priority**: Must Have
- **Description**: Authentication via Supabase Auth with @supabase/ssr
- **Acceptance Criteria**:
  - Secure user registration and login with OAuth providers
  - Profile management interface using Shadcn/ui components
  - Password recovery functionality via Supabase Auth
  - Account settings and preferences with real-time sync

#### REQ-FUN-007: Community Platform
- **Priority**: Should Have
- **Description**: WhatsApp groups for user engagement and support
- **Acceptance Criteria**:
  - WhatsApp Business integration for community management
  - User onboarding and engagement workflows via WhatsApp
  - Feedback collection and feature request systems through voice messages
  - Tutorial and documentation access via WhatsApp broadcasts

#### REQ-FUN-008: Analytics Dashboard
- **Priority**: Must Have
- **Description**: PostHog-powered usage analytics and performance reporting
- **Acceptance Criteria**:
  - User activity tracking via PostHog integration
  - Performance metrics display with animated charts
  - Custom report generation using Shadcn/ui components
  - Data export capabilities for user insights

#### REQ-FUN-009: Wallet-Based Billing System
- **Priority**: Must Have
- **Description**: Wallet-based pay-per-use billing system with African market optimization and global payment standards
- **Acceptance Criteria**:
  - **Wallet Loading**: $5+ options for all users (individual and enterprise) - universal accessibility
  - **AI Model Pricing**: Gemini 2.5 Pro ($1.25/$10.00 per 1M tokens), DeepSeek Coder ($0.27/$1.10), DeepSeek R1 ($0.55/$2.19), Gemini Flash ($0.15/$0.60)
  - **Real-Time Cost Calculation**: $0.068 average cost, $0.09 average revenue per query (32% gross margin through intelligent routing)
  - **Optional Transparency**: Users can toggle cost estimates and detailed token breakdowns on/off

#### REQ-FUN-010: Custom Cost Calculator & Markup Engine
- **Priority**: Must Have
- **Description**: Custom-built cost calculation and markup system for comprehensive production cost accounting and profit margin management
- **Acceptance Criteria**:
  - **Frontend Cost Estimation**: Pre-query token counting and cost estimation using @dqbd/tiktoken for user transparency
  - **Backend Cost Tracking**: Real-time cost calculation with Langfuse integration for actual usage tracking
  - **Comprehensive Cost Structure**: Account for all production costs including AI models, infrastructure, operations, payment processing
  - **Dynamic Markup Logic**: Apply 32% gross margin with configurable markup rules based on query complexity and model usage
  - **Financial Precision**: Use decimal.js for all financial calculations to prevent floating-point errors
  - **Production Cost Components**:
    - AI model costs (base rates from providers)
    - Infrastructure costs (Supabase $0.0001, Vercel $0.0002, Cloudflare $0.00005 per query)
    - Operations costs (support $0.002, development $0.003, marketing $0.001 per query)
    - Payment processing fees (1.5-2.9% based on provider)
  - **Profit Margin Management**: 32% gross margin, 15% net margin target with 5% contingency buffer
  - **Cost Optimization**: Intelligent model routing to minimize costs while maintaining quality
  - **Auto-Refill**: Automatic wallet replenishment when balance drops below configurable threshold ($2.00 default)
  - **Smart Warnings**: Alerts for expensive queries (>$0.50) and low balance notifications
  - **Payment Processing**: Primary: Paystack Ghana (1.95% fees), Flutterwave Nigeria (2.0% local, 4.8% international); Backup: Stripe and PayPal for global accessibility
  - **Transaction Management**: Real-time balance tracking and transaction history with detailed spending breakdown
  - **Regional Support**: Currency display (GHS, NGN) with USD billing for wallet loads
  - **Analytics Integration**: Usage analytics and spending insights with tRPC type-safe APIs
  - **Enterprise Features**: Team wallets, shared budgets, admin controls, usage monitoring, spending limits, invoice billing (typical loads: $200-1,000 based on usage patterns)

#### REQ-FUN-010: Wallet-Based Affiliate Program
- **Priority**: Should Have
- **Description**: Affiliate program optimized for wallet-based pay-per-use model with African market accessibility
- **Acceptance Criteria**:
  - **Commission Structure**: 20% on initial wallet loads (max $25), 10% on recurring loads 2-3 (max $15), 5% on loads 4+ (max $10)
  - **Enterprise Commissions**: 15% on team/enterprise wallet loads (max $50)
  - **Performance Bonuses**: 3-5% monthly bonus for high-performing affiliates (>$50-100 monthly commissions)
  - **Minimum Payout**: $10 equivalent in local currency (reduced for African market accessibility)
  - **Payment Processing**: MTN Mobile Money, Paystack, Flutterwave, PayPal, Wise integration
  - **Tracking System**: Real-time commission tracking, load number counting, performance analytics
  - **Cookie Duration**: 90 days (extended to capture recurring wallet loads)
  - **Referral Links**: Unique affiliate codes, QR code generation, performance analytics dashboard
  - **Regional Optimization**: Local currency display, mobile money payouts, reduced minimum thresholds

### 2.3 AI-Powered Capabilities

#### REQ-FUN-011: Context Engine & Memory Management
- **Priority**: Must Have
- **Description**: Comprehensive context engine leveraging Agno's built-in memory and session management capabilities
- **Acceptance Criteria**:
  - **Session Persistence**: Multi-turn conversation memory across user sessions via Agno framework
  - **Cross-Agent Context**: Shared context between Data Analysis, Visualization, and Voice Processing agents
  - **Excel Context Awareness**: Understanding of workbook structure, cell references, and data relationships
  - **Vector Database Integration**: Semantic search across conversation history using Agno's 20+ supported databases
  - **User Context Profiles**: Personalized context based on user preferences, analysis patterns, and regional settings
  - **Real-time Context Sync**: Live context synchronization between Excel add-in and web application via Supabase Realtime
  - **Context Recovery**: Ability to resume interrupted analysis sessions with full context restoration

#### REQ-FUN-012: Natural Language Processing - OPTIMIZED
- **Priority**: Must Have
- **Description**: Cost-optimized NLP via Agno 1.2.2 framework with intelligent orchestrator model architecture for maximum efficiency
- **Acceptance Criteria**:
  - 90%+ accuracy in query interpretation using orchestrator architecture with Excel-native integration
  - Orchestrator Model: Gemini 2.5 Pro (2M token context) as main orchestrator that intelligently delegates to specialist models
  - Specialist Models: DeepSeek Coder (128K context, coding tasks), Gemini Flash (1M context, cost-efficient operations), DeepSeek R1-0528 (64K context, advanced reasoning)
  - Context-aware conversation management through Agno multi-agent system
  - Multi-turn dialogue support with conversation memory
  - Cost per query: $0.068 average cost, $0.09 average revenue (32% gross margin through intelligent routing)
  - Average usage: 68-136 queries per user per month (validated usage patterns from financial analysis)
  - Users don't choose AI models directly - system intelligently routes Excel and Python code generation tasks to appropriate cost-efficient models

> **⚠️ CRITICAL USAGE PATTERN WARNING**: Internal research indicates actual usage patterns may be 2-3x higher than conservative projections, with 500-800 queries/month average. Financial projections use conservative estimates that may require significant revision based on real-world usage data.

#### REQ-FUN-013: Computer Vision & OCR
- **Priority**: Should Have
- **Description**: Multi-modal AI models for document processing
- **Acceptance Criteria**:
  - OCR via GPT-4 Vision, Claude 3 Vision, Gemini Vision models
  - Table structure recognition through multi-modal AI capabilities
  - Receipt and invoice processing via vision models in Agno framework
  - Batch processing capabilities using AI model APIs

#### REQ-FUN-014: Data Integration (Hybrid Architecture)
- **Priority**: Must Have
- **Description**: Hybrid database access combining traditional drivers with Model Context Protocol for AI-enhanced features
- **Acceptance Criteria**:
  - **Phase 1 (MVP) - Traditional Database Drivers**:
    - Local file access (Excel, CSV, PDF) via File System Access API
    - Cloud integration (OneDrive via Microsoft Graph, Google Sheets API v4)
    - Direct database connections: PostgreSQL, MySQL, Microsoft SQL Server, Oracle, Snowflake
    - Business system integration: Salesforce CRM, Zoho CRM, QuickBooks Online APIs
    - Primary database through Supabase PostgreSQL 15.x
    - Performance-optimized for African market connectivity challenges
  - **Phase 2 (Enhancement) - Advanced AI Integration**:
    - **Model Context Protocol (MCP)**: MCP servers for each supported data source
    - **AG-UI Protocol**: Real-time agent-user interaction with event-driven communication
    - Natural language database queries via AI models
    - Cross-database analytics and schema discovery
    - Interactive AI workflows with human-in-the-loop collaboration
    - Streaming AI responses and real-time state synchronization
    - Dynamic tool discovery and self-describing database capabilities
    - AI-mediated data access with fallback to traditional drivers
  - **Hybrid Features**:
    - Automatic routing between direct and MCP access based on query type
    - Connection method selection (direct, MCP, or hybrid)
    - Fallback strategies for connectivity issues
    - Web data extraction capabilities via AI models and web scraping

#### REQ-FUN-015: Animated User Interface
- **Priority**: Should Have
- **Description**: Enhanced user experience through animated components and visual feedback
- **Acceptance Criteria**:
  - Animated loading states during AI code execution
  - Visual feedback for successful/failed operations
  - Smooth transitions between different interface states
  - Animated progress indicators for long-running operations
  - Interactive hover effects and micro-interactions

#### REQ-FUN-016: Visual Feedback System
- **Priority**: Must Have
- **Description**: Clear visual communication of system status and user actions
- **Acceptance Criteria**:
  - Real-time status indicators for AI processing
  - Animated success/error notifications
  - Progress visualization for data analysis tasks
  - Interactive elements with hover and click animations
  - Contextual tooltips with smooth animations

---

## 3. Non-Functional Requirements

### 3.1 Performance Requirements

#### REQ-NFR-001: Response Time
- **Priority**: Must Have
- **Description**: 95% of operations complete within 3 seconds
- **Acceptance Criteria**:
  - Query processing ≤ 3 seconds
  - Chart generation ≤ 2 seconds
  - Voice recognition ≤ 1 second
  - Formula generation ≤ 2 seconds

#### REQ-NFR-002: System Availability
- **Priority**: Must Have
- **Description**: 99% uptime using Vercel/Supabase managed services
- **Acceptance Criteria**:
  - Maximum 7 hours downtime per month (realistic for managed services)
  - Automated failover via Vercel and Supabase infrastructure
  - Health monitoring via Sentry 9.24.0 and PostHog
  - Basic disaster recovery through managed service backups

#### REQ-NFR-003: Scalability
- **Priority**: Should Have
- **Description**: Support for 100-500 concurrent users (MVP scale)
- **Acceptance Criteria**:
  - Horizontal scaling via Vercel Edge Functions
  - Load balancing through Vercel and Supabase infrastructure
  - Database performance optimization using Supabase connection pooling
  - CDN integration via Cloudflare with African edge locations for optimal performance

### 3.2 Security Requirements

#### REQ-NFR-004: Data Protection
- **Priority**: Must Have
- **Description**: Basic GDPR compliance using Supabase security features
- **Acceptance Criteria**:
  - End-to-end encryption via Supabase built-in security
  - Data anonymization capabilities through Supabase RLS
  - User consent management via simple consent forms
  - Right to deletion implementation through Supabase APIs

#### REQ-NFR-005: Access Control
- **Priority**: Must Have
- **Description**: Basic access control using Supabase Auth and RLS
- **Acceptance Criteria**:
  - OAuth authentication via Supabase Auth providers
  - Role-based permissions through Supabase RLS policies
  - Session management via @supabase/ssr
  - Basic audit logging through Supabase built-in features

#### REQ-NFR-006: Execution Security
- **Priority**: Must Have
- **Description**: Hybrid sandbox security (Pyodide WebAssembly + E2B)
- **Acceptance Criteria**:
  - WebAssembly isolation for client-side Pyodide execution
  - E2B Code Interpreter security for server-side execution
  - Resource usage limits through sandbox configurations
  - Basic security through managed sandbox services

### 3.3 Usability Requirements

#### REQ-NFR-007: User Interface
- **Priority**: Must Have
- **Description**: Intuitive interface with modern design system and animated feedback
- **Acceptance Criteria**:
  - Consistent design patterns using Shadcn/ui components
  - Accessibility compliance (WCAG 2.1)
  - Desktop-responsive design optimized for Excel taskpane
  - Dark/light theme support
  - Smooth animations and micro-interactions using Magic UI components

#### REQ-NFR-008: Multilingual Interface
- **Priority**: Must Have
- **Description**: Complete bilingual interface (English/French)
- **Acceptance Criteria**:
  - Full UI translation
  - Cultural adaptation
  - Right-to-left text support
  - Locale-specific formatting

---

## 4. Technical Requirements

### 4.1 Platform Requirements

#### REQ-TEC-001: Excel Compatibility
- **Priority**: Must Have
- **Description**: Support for Excel Desktop, Mac, and Online
- **Acceptance Criteria**:
  - Office.js ExcelApi 1.17+ compatibility
  - Cross-platform functionality
  - Backward compatibility to ExcelApi 1.4
  - Security sandbox compliance

#### REQ-TEC-002: Web Browser Support
- **Priority**: Must Have
- **Description**: Modern web browser compatibility
- **Acceptance Criteria**:
  - Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
  - Progressive Web App capabilities
  - Offline functionality
  - Desktop browser optimization

#### REQ-TEC-003: Technology Stack
- **Priority**: Must Have
- **Description**: Modern, maintainable technology stack with enhanced UI capabilities
- **Acceptance Criteria**:
  - React 19+ with TypeScript 5.6+
  - Next.js 15+ with App Router
  - Supabase for backend services
  - Shadcn/ui for core component library
  - Magic UI for animated components and visual effects
  - Framer Motion for animation engine
  - Tailwind CSS for styling system

### 4.2 Integration Requirements

#### REQ-TEC-004: Authentication Integration
- **Priority**: Must Have
- **Description**: Secure authentication across Excel add-in and web app
- **Acceptance Criteria**:
  - Single sign-on (SSO) capability
  - OAuth provider integration
  - Session synchronization
  - Token-based authentication

#### REQ-TEC-005: Real-time Synchronization
- **Priority**: Should Have
- **Description**: Live data synchronization between components
- **Acceptance Criteria**:
  - Real-time updates across devices
  - Conflict resolution mechanisms
  - Offline sync capabilities
  - Event-driven architecture

---

## 5. User Requirements

### 5.1 User Stories

#### REQ-USR-001: Business Analyst Data Analysis
- **As a** business analyst
- **I want to** analyze sales data using natural language queries
- **So that** I can quickly identify trends and insights
- **Acceptance Criteria**:
  - Can ask "Show me sales trends for Q3" and get relevant charts
  - Can drill down into specific regions or products
  - Can export analysis results for presentations

#### REQ-USR-002: Team Administrator Management
- **As a** team administrator or department manager
- **I want to** manage team wallets and access controls
- **So that** I can ensure proper usage governance and cost management
- **Acceptance Criteria**:
  - Can access standardized templates and admin workflows
  - Can access team usage analytics and admin dashboard
  - Can manage team wallets, shared budgets, and user permissions

#### REQ-USR-003: SME Owner Business Intelligence
- **As a** small business owner
- **I want to** understand my business performance through simple questions
- **So that** I can make informed decisions without hiring analysts
- **Acceptance Criteria**:
  - Can ask business questions in local language (English/French)
  - Receives actionable insights and recommendations
  - Can access affordable pricing tiers

---

## 6. Compliance and Regional Requirements

### 6.1 African Market Requirements

#### REQ-REG-001: Payment Processing
- **Priority**: Must Have
- **Description**: Global-standard payment systems optimized for African markets
- **Acceptance Criteria**:
  - Primary: African payment providers (Paystack, Flutterwave) for local optimization
  - Secondary: Global payment providers (Stripe, PayPal) for reliability and expansion
  - Multi-currency support (GHS, NGN, USD, EUR)
  - Local banking integration with international standards
  - Enterprise-grade security and compliance

#### REQ-REG-002: Data Sovereignty
- **Priority**: Must Have
- **Description**: Global data protection standards with African regulatory compliance
- **Acceptance Criteria**:
  - GDPR compliance (global standard)
  - Ghana Data Protection Act compliance
  - Regional data storage options with global backup
  - Cross-border data transfer controls
  - Local regulatory reporting with international audit trails

#### REQ-REG-003: Infrastructure Optimization
- **Priority**: Should Have
- **Description**: Global-standard performance optimized for African internet infrastructure
- **Acceptance Criteria**:
  - Bandwidth-efficient operations meeting global standards
  - Regional CDN deployment with worldwide coverage
  - Offline functionality for unreliable connections
  - Progressive loading capabilities with graceful degradation

---

## 7. Success Metrics and Validation

### 7.1 Acceptance Testing Criteria

Each requirement will be validated through:
- **Unit Testing**: Individual component functionality
- **Integration Testing**: Component interaction validation
- **User Acceptance Testing**: End-user validation in target markets
- **Performance Testing**: Load and stress testing
- **Security Testing**: Penetration testing and vulnerability assessment

### 7.2 Success Metrics (Solo Developer + AI-Assisted Development)

- **Technical**: 90% test coverage, 99% uptime, sub-3-second response times
- **Business**: 1,000-10,000 users in 6 months, $90K-1.8M ARR in 12 months (Conservative: $90K, Realistic: $675K, Optimistic: $1.8M - validated financial model)
- **User**: 70%+ retention after 30 days, 4.0+ star rating, 80%+ would recommend
- **Financial**: Break-even Month 1-2 with 67+ active users, 32% gross margins (validated financial analysis)
- **Validation**: 50+ user interviews, working Excel add-in with AI capabilities, profitable unit economics (32% margin) on every transaction

### 7.3 Strategic Pivot: Subscription to Wallet-Based Model

#### **Previous Subscription Model (FAILED):**
- Professional Tier: $20/month revenue, $75.46 cost = **$55.46 LOSS per user**
- Team Tier: $18/month revenue, $95.89 cost = **$77.89 LOSS per user**
- Break-even: Impossible at any reasonable pricing level
- Market Position: Unsustainable, required 275-433% price increases

#### **New Wallet-Based Model (PROFITABLE):**
- Revenue per Query: $0.09 average
- Cost per Query: $0.068 average
- Gross Margin: 32% per query
- Break-even: 67 active users (vs. impossible under subscriptions)

### 7.4 Revised Requirements Rationale

#### **Why These Requirements Are Realistic:**
- **Budget**: $9.5K-17.9K operational costs with optimized AI model stack and wallet-based pricing (validated financial analysis)
- **Scale**: 1,000-10,000 users achievable through pay-per-use accessibility vs. subscription barriers
- **Technology**: Modern stack (React 19, Supabase, Agno) enables solo development with AI assistance
- **Security**: Basic compliance via managed services vs. expensive SOC II certification
- **Revenue**: $90K-1.8M ARR validated through June 2025 wallet-based financial model (Conservative: $90K, Realistic: $675K, Optimistic: $1.8M)
- **Unit Economics**: 32% gross margins achieved with 20-33% markup on all queries ensuring profitability (validated financial analysis)

#### **Requirements Removed/Modified:**
- **Subscription Model**: 3-tier subscription replaced with wallet-based pay-per-use pricing
- **Complex Infrastructure**: 99.5% uptime reduced to 99% using managed services
- **Limited Scale**: 100-500 users expanded to 1,000-10,000 with pay-per-use accessibility
- **Expensive Compliance**: SOC II removed, basic GDPR via Supabase features
- **Large Partnerships**: 50+ institutions replaced with community building focus

---

*This requirements document serves as the foundation for development planning and will be updated as the project evolves.*
