# Overview
Excella is an intelligent Excel add-in and web platform that democratizes advanced data analysis for African business professionals, SME owners, and team administrators. It enables users to perform complex analytics, generate visualizations, and automate workflows using natural language and voice commands, all within Excel. Excella's wallet-based pay-per-use model ensures affordability and profitability, while its modern, scalable architecture leverages the latest AI, cloud, and frontend technologies.

# Core Features
1. Conversational Interface: Sidebar chat/voice assistant in Excel (React 19, Shadcn/ui), multi-turn conversation, context memory, agent/chat mode toggle, personalized greetings, suggested actions, live status indicator
2. Multilingual Support: English and French, automatic detection, seamless switching, voice input via OpenAI Whisper API
3. Hybrid Code Execution Sandbox: Client-side Pyodide (WebAssembly) for offline Python execution, server-side E2B Code Interpreter for complex tasks, automatic fallback, security isolation
4. Data Analysis Engine: Statistical analysis (pandas, NumPy, scipy, scikit-learn), pattern detection, hypothesis testing, AI-powered insights
5. Visualization Generation: Automatic chart selection (matplotlib, plotly), interactive dashboards, direct Excel embedding, export options
6. User Management & Billing: Supabase Auth, OAuth, profile management, wallet-based pay-per-use billing (Paystack, Flutterwave, Stripe), real-time cost calculation, auto-refill, transaction history
7. Analytics Dashboard: PostHog-powered usage analytics, animated charts, custom reports
8. Community & Support: WhatsApp integration for onboarding, support, and feedback, affiliate program with wallet-based commissions

# User Experience
Personas: Business analysts, SME owners, team admins (non-technical, price-sensitive, need actionable insights)
Key Flows:
  1. Install add-in → Authenticate → Start chat/voice query
  2. Analyze data → View results/visualizations → Export/share
  3. Manage wallet → Load funds → Track usage/costs
  4. Access support/community via WhatsApp
UI/UX: Modern, Apple- and Notion-inspired design; animated feedback; bilingual interface; responsive for Excel taskpane; accessibility and dark/light mode

# Technical Architecture
Frontend: React 19, Next.js 15, TypeScript 5.6, Tailwind CSS, Shadcn/ui, Magic UI, Framer Motion, Zustand
Excel Integration: Office.js ExcelApi 1.17+, office-addin-manifest
Backend: Supabase (PostgreSQL 15.x, Edge Functions, Realtime, Storage), tRPC 11.x, Deno 2.x
AI Orchestration: Agno 1.2.2 (multi-agent, 23+ model providers), OpenAI, OpenRouter, Google Vertex AI
Python Microservices: FastAPI, Uvicorn, Pyodide, E2B
Cost Calculation: Custom calculator (decimal.js, @dqbd/tiktoken, Langfuse for analytics)
Monitoring: PostHog, Sentry, OpenReplay, Upstash (rate limiting)
CDN/Deployment: Cloudflare, Vercel
Payments: Paystack, Flutterwave, Stripe, PayPal

# Development Roadmap
Phase 1: MVP
- Excel add-in: Conversational UI, voice input, basic data analysis, wallet billing, English/French support
- Web app: User management, wallet dashboard, analytics
- Hybrid sandbox: Pyodide + E2B integration
- Cost calculator: Pre-query estimation, real-time tracking

Phase 2: Enhancements
- Advanced AI workflows (multi-agent, context memory)
- Computer vision (OCR, document processing)
- Affiliate program, team wallets, admin controls
- Enhanced analytics, community features, regional templates

Phase 3: Scaling
- Production infrastructure, performance optimization
- Enterprise features, advanced compliance, regional expansion

# Logical Dependency Chain
1. Foundation: Set up Supabase, Vercel, Cloudflare, payment providers; implement authentication, wallet, and user management
2. Core Add-in: Build Excel sidebar UI, integrate Office.js; add chat/voice interface, connect to backend
3. AI/Analysis: Integrate Agno, implement hybrid sandbox; add data analysis, visualization, and cost calculation
4. Web App: Analytics dashboard, profile management, documentation
5. Enhancements: Multilingual support, affiliate program, community integration; computer vision, advanced workflows

# Risks and Mitigations
- Free Tier Dependency: Plan transition to paid tiers, monitor usage, build revenue early
- Pricing Shock: Gradual increases, value demonstration, user grandfathering
- Cost Escalation: Strict monitoring, usage caps, emergency reduction protocols
- Solo Developer Constraint: AI-assisted development, phased delivery, community support
- Regional Payment/Compliance: Use local providers, align with Ghana/Nigeria data laws

# Appendix
- Research Findings: 10-phase tech validation, cost model, African market analysis, wallet-based pricing validation, competitive analysis (Julius AI, Copilot, ChatGPT)
- Technical Specs: See .excella/core/technical-stack.md for full stack, version matrix, and migration requirements; see .excella/core/requirements.md for detailed functional, non-functional, and business requirements; see .excella/core/project-brief.md for objectives, constraints, and success metrics 