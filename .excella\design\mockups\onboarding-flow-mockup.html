<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excella Onboarding Flow - Visual Mockup</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#000000',
                        accent: '#666666',
                        muted: '#f8f9fa',
                        'muted-foreground': '#6b7280'
                    }
                }
            }
        }
    </script>
    <style>
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-active {
            background: #000000;
            color: white;
        }
        .step-completed {
            background: #000000;
            color: white;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 rounded-lg bg-black flex items-center justify-center">
                        <span class="text-white font-bold text-sm">E</span>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-900">Excella</h1>
                </div>
                <div class="text-sm text-gray-500">
                    Need help? <a href="#" class="text-black hover:underline">Contact Support</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Progress Indicator -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-2xl font-bold text-gray-900">Get Started with Excella</h2>
                <span class="text-sm text-muted-foreground">Step <span id="current-step">1</span> of 5</span>
            </div>
            <div class="flex items-center space-x-4">
                <div class="step-indicator step-active w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">1</div>
                <div class="flex-1 h-2 bg-gray-200 rounded-full">
                    <div class="h-2 bg-black rounded-full transition-all duration-500" style="width: 20%"></div>
                </div>
                <div class="step-indicator w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium text-gray-500">2</div>
                <div class="flex-1 h-2 bg-gray-200 rounded-full"></div>
                <div class="step-indicator w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium text-gray-500">3</div>
                <div class="flex-1 h-2 bg-gray-200 rounded-full"></div>
                <div class="step-indicator w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium text-gray-500">4</div>
                <div class="flex-1 h-2 bg-gray-200 rounded-full"></div>
                <div class="step-indicator w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium text-gray-500">5</div>
            </div>
        </div>

        <!-- Step Content Container -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <!-- Step 1: Registration -->
            <div id="step-1" class="step-content fade-in">
                <div class="text-center mb-8">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">Welcome to Excella</h3>
                    <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                        Transform your Excel data analysis with AI-powered insights. Join thousands of professionals across Africa who trust Excella for their data needs.
                    </p>
                </div>

                <div class="max-w-md mx-auto space-y-4">
                    <!-- OAuth Providers -->
                    <button class="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5" viewBox="0 0 24 24">
                            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span class="text-gray-900">Continue with Google</span>
                    </button>

                    <button class="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-5 h-5" fill="#00a1f1" viewBox="0 0 24 24">
                            <path d="M23.5 12c0-6.35-5.15-11.5-11.5-11.5S.5 5.65.5 12c0 5.74 4.21 10.49 9.71 11.35v-8.03H7.37V12h2.84V9.8c0-2.8 1.67-4.35 4.22-4.35 1.22 0 2.5.22 2.5.22v2.75h-1.41c-1.39 0-1.82.86-1.82 1.75V12h3.1l-.5 3.32h-2.6v8.03C19.29 22.49 23.5 17.74 23.5 12z"/>
                        </svg>
                        <span class="text-gray-900">Continue with Microsoft</span>
                    </button>

                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-muted-foreground">or</span>
                        </div>
                    </div>

                    <!-- Email Form -->
                    <div class="space-y-4">
                        <input type="email" placeholder="Enter your email address" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                        <input type="password" placeholder="Create a password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                        <button class="w-full bg-black text-white py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                            Create Account
                        </button>
                    </div>

                    <p class="text-xs text-center text-gray-500">
                        By creating an account, you agree to our
                        <a href="#" class="text-black hover:underline">Terms of Service</a> and
                        <a href="#" class="text-black hover:underline">Privacy Policy</a>
                    </p>
                </div>
            </div>

            <!-- Step 2: Profile Setup (Hidden by default) -->
            <div id="step-2" class="step-content hidden">
                <div class="text-center mb-8">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">Tell us about yourself</h3>
                    <p class="text-lg text-muted-foreground">
                        Help us customize Excella for your specific needs and business context.
                    </p>
                </div>

                <div class="max-w-2xl mx-auto space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="text" placeholder="First Name" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                        <input type="text" placeholder="Last Name" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                    </div>

                    <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                        <option>Select your country</option>
                        <option>Ghana</option>
                        <option>Nigeria</option>
                        <option>Côte d'Ivoire</option>
                        <option>Senegal</option>
                        <option>Other</option>
                    </select>

                    <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                        <option>What best describes your role?</option>
                        <option>Business Analyst</option>
                        <option>SME Owner</option>
                        <option>Team Leader/Manager</option>
                        <option>Data Analyst</option>
                        <option>Other</option>
                    </select>

                    <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent">
                        <option>Company size</option>
                        <option>Just me</option>
                        <option>2-10 employees</option>
                        <option>11-50 employees</option>
                        <option>51-200 employees</option>
                        <option>200+ employees</option>
                    </select>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary use cases (select all that apply)</label>
                        <div class="grid grid-cols-2 gap-3">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="rounded border-gray-300 text-black focus:ring-black">
                                <span class="text-sm">Data Analysis</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="rounded border-gray-300 text-black focus:ring-black">
                                <span class="text-sm">Reporting</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="rounded border-gray-300 text-black focus:ring-black">
                                <span class="text-sm">Visualization</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="rounded border-gray-300 text-black focus:ring-black">
                                <span class="text-sm">Forecasting</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                <button id="prev-btn" class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    ← Previous
                </button>
                <div class="flex-1"></div>
                <button id="next-btn" class="px-8 py-3 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors">
                    Continue →
                </button>
            </div>
        </div>

        <!-- Benefits Section -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h4 class="font-semibold text-gray-900 mb-2">AI-Powered Analysis</h4>
                <p class="text-sm text-gray-600">Get instant insights from your Excel data using natural language queries</p>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h4 class="font-semibold text-gray-900 mb-2">Team Collaboration</h4>
                <p class="text-sm text-gray-600">Share insights and collaborate with your team seamlessly</p>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h4 class="font-semibold text-gray-900 mb-2">Secure & Compliant</h4>
                <p class="text-sm text-gray-600">Enterprise-grade security with African data protection compliance</p>
            </div>
        </div>
    </main>

    <script>
        // Simple step navigation simulation
        let currentStep = 1;
        const totalSteps = 5;

        document.getElementById('next-btn').addEventListener('click', function() {
            if (currentStep < totalSteps) {
                // Hide current step
                document.getElementById(`step-${currentStep}`).classList.add('hidden');
                
                // Show next step
                currentStep++;
                if (document.getElementById(`step-${currentStep}`)) {
                    document.getElementById(`step-${currentStep}`).classList.remove('hidden');
                    document.getElementById(`step-${currentStep}`).classList.add('fade-in');
                }
                
                // Update progress
                updateProgress();
                
                // Show/hide navigation buttons
                if (currentStep > 1) {
                    document.getElementById('prev-btn').classList.remove('hidden');
                }
            }
        });

        function updateProgress() {
            document.getElementById('current-step').textContent = currentStep;
            const progressBar = document.querySelector('.h-2.bg-gradient-to-r');
            const percentage = (currentStep / totalSteps) * 100;
            progressBar.style.width = `${percentage}%`;
            
            // Update step indicators
            document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
                const stepNumber = index + 1;
                indicator.classList.remove('step-active', 'step-completed');
                
                if (stepNumber < currentStep) {
                    indicator.classList.add('step-completed');
                } else if (stepNumber === currentStep) {
                    indicator.classList.add('step-active');
                }
            });
        }
    </script>
</body>
</html>
