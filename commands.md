Here is the full list of Taskmaster C<PERSON><PERSON> commands you can use to manage your project:

---

### **Initialization & Setup**
- `task-master init`  
  Initialize Taskmaster in your project directory.

- `task-master parse-prd <file>`  
  Parse a Product Requirements Document (PRD) to generate tasks.

- `task-master models`  
  View or set the AI models used by Taskmaster.

---

### **Task Listing & Viewing**
- `task-master list`  
  List all tasks.

- `task-master list --with-subtasks`  
  List all tasks with their subtasks.

- `task-master show <id>`  
  Show details for a specific task or subtask.

- `task-master next`  
  Show the next actionable task based on dependencies and status.

---

### **Task Creation & Modification**
- `task-master add-task --prompt "<description>"`  
  Add a new task using AI.

- `task-master add-subtask --parent <id> --title "<title>"`  
  Add a subtask to a parent task.

- `task-master update --from <id> --prompt "<context>"`  
  Update multiple future tasks starting from a specific ID.

- `task-master update-task --id <id> --prompt "<context>"`  
  Update a specific task.

- `task-master update-subtask --id <id> --prompt "<context>"`  
  Append notes or details to a subtask.

- `task-master set-status --id <id> --status <status>`  
  Set the status of a task or subtask (e.g., done, in-progress).

- `task-master remove-task --id <id>`  
  Remove a task or subtask.

- `task-master remove-subtask --id <id>`  
  Remove a subtask from its parent.

---

### **Task Structure & Breakdown**
- `task-master expand --id <id>`  
  Expand a task into subtasks.

- `task-master expand --all`  
  Expand all eligible tasks into subtasks.

- `task-master clear-subtasks --id <id>`  
  Remove all subtasks from a parent task.

- `task-master move --from <id> --to <id>`  
  Move a task or subtask to a new position.

---

### **Dependency Management**
- `task-master add-dependency --id <id> --depends-on <id>`  
  Add a dependency between tasks.

- `task-master remove-dependency --id <id> --depends-on <id>`  
  Remove a dependency.

- `task-master validate-dependencies`  
  Check for dependency issues.

- `task-master fix-dependencies`  
  Automatically fix dependency issues.

---

### **Analysis & Reporting**
- `task-master analyze-complexity`  
  Analyze task complexity and suggest breakdowns.

- `task-master complexity-report`  
  View the complexity analysis report.

---

### **File Management**
- `task-master generate`  
  Generate or update Markdown files for each task.

---

### **Help**
- `task-master --help`  
  Show help and all available commands.

---

**Tip:**  
You can always run `task-master <command> --help` for detailed usage and options for any command.

Let me know if you want examples or details for any specific command!