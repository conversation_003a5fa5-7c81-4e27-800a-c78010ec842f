// tRPC Configuration for Excella
// Authentication and context setup with Supabase

import { initTRPC, TRPCError } from '@trpc/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';

// =====================================================
// CONTEXT CREATION
// =====================================================

interface CreateContextOptions {
  req?: Request;
  res?: Response;
}

export async function createContext(opts: CreateContextOptions) {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Get auth token from request headers
  const authHeader = opts.req?.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '');

  let user = null;
  if (token) {
    try {
      const { data: { user: authUser }, error } = await supabase.auth.getUser(token);
      if (!error && authUser) {
        user = authUser;
      }
    } catch (error) {
      console.error('Auth error:', error);
    }
  }

  return {
    supabase,
    user,
    req: opts.req,
    res: opts.res
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;

// =====================================================
// TRPC INITIALIZATION
// =====================================================

const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof z.ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

// =====================================================
// MIDDLEWARE
// =====================================================

// Authentication middleware
const isAuthenticated = t.middleware(({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required'
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: ctx.user, // Type-safe user context
    },
  });
});

// Usage tracking middleware
const trackUsage = t.middleware(async ({ ctx, next, path, type }) => {
  const start = Date.now();
  
  try {
    const result = await next();
    
    // Track successful API calls
    if (ctx.user && type === 'mutation') {
      const duration = Date.now() - start;
      
      // Log API usage for analytics
      console.log(`API Call: ${path} - User: ${ctx.user.id} - Duration: ${duration}ms`);
      
      // TODO: Integrate with PostHog for analytics
      // posthog.capture('api_call', {
      //   user_id: ctx.user.id,
      //   endpoint: path,
      //   duration,
      //   success: true
      // });
    }
    
    return result;
  } catch (error) {
    // Track failed API calls
    if (ctx.user) {
      const duration = Date.now() - start;
      console.error(`API Error: ${path} - User: ${ctx.user.id} - Duration: ${duration}ms - Error:`, error);
      
      // TODO: Integrate with PostHog for error tracking
      // posthog.capture('api_error', {
      //   user_id: ctx.user.id,
      //   endpoint: path,
      //   duration,
      //   error: error.message
      // });
    }
    
    throw error;
  }
});

// =====================================================
// PROCEDURE DEFINITIONS
// =====================================================

// Base router
export const router = t.router;

// Public procedure (no auth required)
export const publicProcedure = t.procedure.use(trackUsage);

// Protected procedure (auth required)
export const protectedProcedure = t.procedure
  .use(trackUsage)
  .use(isAuthenticated);

// Admin procedure (admin role required)
export const adminProcedure = t.procedure
  .use(trackUsage)
  .use(isAuthenticated)
  .use(async ({ ctx, next }) => {
    // Check if user has admin role
    const { data: user, error } = await ctx.supabase
      .from('users')
      .select('subscription_tier, email')
      .eq('id', ctx.user.id)
      .single();

    // TODO: Implement proper admin role checking
    // For now, check if user is in admin email list
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(user?.email || '');

    if (!isAdmin) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'Admin access required'
      });
    }

    return next({
      ctx: {
        ...ctx,
        user: ctx.user,
        isAdmin: true
      }
    });
  });

// =====================================================
// HELPER FUNCTIONS
// =====================================================

// Check if user has reached query limits
export async function checkQueryLimit(supabase: SupabaseClient, userId: string): Promise<boolean> {
  const { data: user, error } = await supabase
    .from('users')
    .select('subscription_tier, monthly_queries_used, monthly_queries_limit')
    .eq('id', userId)
    .single();

  if (error || !user) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to check query limits'
    });
  }

  // Free tier has limits, Professional/Team are unlimited
  if (user.subscription_tier === 'free') {
    return user.monthly_queries_used < user.monthly_queries_limit;
  }

  return true; // Unlimited for paid tiers
}

// Process wallet transaction (updated for embedded wallet in users table)
export async function processWalletTransaction(
  supabase: SupabaseClient,
  userId: string,
  costCents: number,
  metadata: {
    tokensUsed?: number;
    modelUsed?: string;
    sandboxMethod?: string;
    conversationId?: string;
    messageId?: string;
    description: string;
  }
): Promise<{ success: boolean; transactionId?: string; error?: string }> {
  try {
    // Use the stored procedure from migration
    const { data, error } = await supabase.rpc('process_wallet_transaction', {
      user_uuid: userId,
      amount_cents: -costCents, // Negative for spend
      transaction_type: 'spend',
      description: metadata.description,
      conversation_id: metadata.conversationId,
      message_id: metadata.messageId,
      model_used: metadata.modelUsed,
      tokens_used: metadata.tokensUsed || 0
    });

    if (error) {
      console.error('Failed to process wallet transaction:', error);
      return { success: false, error: error.message };
    }

    console.log(`Wallet transaction processed for user ${userId}:`, {
      costCents,
      transactionId: data,
      ...metadata
    });

    return { success: true, transactionId: data };
  } catch (error) {
    console.error('Wallet transaction error:', error);
    return { success: false, error: error.message };
  }
}

// =====================================================
// ERROR HANDLING
// =====================================================

export function handleTRPCError(error: unknown): TRPCError {
  if (error instanceof TRPCError) {
    return error;
  }

  if (error instanceof z.ZodError) {
    return new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Invalid input data',
      cause: error
    });
  }

  // Supabase errors
  if (typeof error === 'object' && error !== null && 'code' in error) {
    const supabaseError = error as { code: string; message: string };
    
    switch (supabaseError.code) {
      case 'PGRST116': // No rows returned
        return new TRPCError({
          code: 'NOT_FOUND',
          message: 'Resource not found'
        });
      case '23505': // Unique constraint violation
        return new TRPCError({
          code: 'CONFLICT',
          message: 'Resource already exists'
        });
      case '23503': // Foreign key constraint violation
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid reference'
        });
      default:
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Database error occurred'
        });
    }
  }

  // Generic error
  console.error('Unhandled error:', error);
  return new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred'
  });
}

// =====================================================
// TYPE EXPORTS
// =====================================================

export type Router = typeof router;
export type Procedure = typeof publicProcedure;
export type ProtectedProcedure = typeof protectedProcedure;
export type AdminProcedure = typeof adminProcedure;
