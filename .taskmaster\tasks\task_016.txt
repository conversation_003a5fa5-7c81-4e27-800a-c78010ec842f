# Task ID: 16
# Title: Add Customer Rating System for Restaurant Specials
# Status: pending
# Dependencies: 1, 2
# Priority: medium
# Description: Implement a 5-star rating system for restaurant specials, including public display of ratings, email prompts for feedback, and UI for managing ratings.
# Details:
1. **Database Setup**: Extend the Supabase schema to include tables for ratings (e.g., `ratings` with fields like `special_id`, `user_id`, `rating`, `comment`, `timestamp`).
2. **Backend API**: Create API endpoints for submitting ratings, fetching ratings for a special, and calculating average ratings. Use Supabase functions or custom logic.
3. **Email Integration**: Set up a scheduled job (e.g., using Supabase Edge Functions or a cron job) to send rating prompts 24 hours after order pickup. Include a link to the rating UI.
4. **Frontend UI**:
   - Add a rating component (5-star UI with optional comment field) to the specials detail page.
   - Create a public ratings display section showing average ratings and individual reviews.
   - Admin UI for managing ratings (e.g., filtering, moderation).
5. **Security**: Ensure ratings are tied to authenticated users and validate input to prevent abuse.
6. **Performance**: Optimize queries for fetching ratings and calculating averages to avoid performance issues.

# Test Strategy:
1. **Database**: Verify the `ratings` table schema and test CRUD operations.
2. **API**: Test endpoints with Postman or unit tests (e.g., submit a rating, fetch ratings, calculate averages).
3. **Email**: Trigger test orders and confirm email prompts are sent after 24 hours.
4. **UI**:
   - Test the rating component by submitting ratings and verifying they appear in the public display.
   - Verify the admin UI can filter and moderate ratings.
5. **Edge Cases**: Test with invalid ratings, unauthenticated users, and large datasets for performance.
