# TaskMaster Development Framework: Complete Guide

*A powerful development framework for organizing and managing software projects with AI assistance*

---

## 📋 What is TaskMaster?

### TaskMaster Framework Overview

TaskMaster is a **development framework and tool** (not an application) that helps organize software projects into manageable tasks, track progress, and coordinate development work. It works through two main interfaces:

1. **MCP Tools** - Called by AI assistants to manage projects programmatically
2. **CLI Interface** - Direct command-line access for visual views and detailed control

### Key Capabilities

- **Project Structure** - Breaks down complex projects into organized tasks and subtasks
- **Dependency Management** - Tracks task relationships and execution order
- **Progress Tracking** - Monitors completion status and project health
- **Complexity Analysis** - Evaluates task difficulty and recommends expansions
- **AI Integration** - Works with AI models for intelligent task generation and updates
- **Visual Reports** - CLI provides charts, graphs, and detailed project views

### How It's Different from Traditional Project Management

**Traditional Tools:**
- Generic task lists without development context
- Manual task creation and organization
- Limited integration with development workflow
- Separate from coding environment

**TaskMaster Framework:**
- Development-focused task structure
- AI-powered task generation from requirements
- Integrated with coding workflow and AI assistants
- Command-line and programmatic access

---

## 🏗️ Architecture and Interfaces

### Two-Interface System

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      YOU        │    │   AI ASSISTANT  │    │   TASKMASTER    │
│  (Developer/    │    │                 │    │   FRAMEWORK     │
│   Manager)      │    │ • Calls MCP     │    │                 │
│                 │    │   tools         │    │ • Task mgmt     │
│ • Direct CLI    │◄──►│ • Executes      │◄──►│ • Dependencies  │
│   access        │    │   code          │    │ • Progress      │
│ • Visual views  │    │ • Reports       │    │ • Analysis      │
│ • Task control  │    │   progress      │    │ • Reports       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Interface 1: AI Assistant + MCP Tools

**How it works:**
- You tell the AI assistant what you want
- AI assistant calls TaskMaster MCP tools on your behalf
- TaskMaster processes the request and returns data
- AI assistant translates results back to you

**What the AI Assistant Does:**
- 🔄 Translates your requests into TaskMaster commands
- 📊 Calls MCP tools for task management
- 💻 Executes code based on task requirements
- � Reports progress and status updates

### Interface 2: Direct CLI Access

**When to use CLI:**
- Visual project overviews and reports
- Detailed task analysis and complexity reports
- Bulk operations on multiple tasks
- Custom queries and data export
- Real-time project monitoring

**CLI Capabilities:**
- `taskmaster get-tasks` - View all tasks with filtering
- `taskmaster next-task` - Find next available task
- `taskmaster complexity-report` - Analyze task complexity
- `taskmaster generate` - Create individual task files
- Visual charts and progress indicators

---

## 🚀 Real-World Usage Examples

### Example 1: Starting a New Project from PRD

**Scenario:** You have a Product Requirements Document (PRD) for an Excel add-in project

#### Via AI Assistant (MCP Interface)
```
You: "I have a PRD for an Excel add-in. Can you help me set up the project structure?"

AI Assistant: "I'll use TaskMaster to initialize the project and parse your PRD."

[AI Assistant calls TaskMaster MCP tools:]
- initialize_project() - Sets up .taskmaster directory structure
- parse_prd() - Converts PRD into organized tasks
- analyze_complexity() - Evaluates task difficulty

AI Assistant: "TaskMaster has created 16 main tasks with 83 subtasks based on your PRD.
The project is estimated at 12-16 weeks. Here's the breakdown..."
```

#### Via CLI (Direct Access)
```bash
# Initialize project
taskmaster init --project-root /path/to/project

# Parse PRD document
taskmaster parse-prd --input prd.txt --num-tasks 15

# View generated tasks
taskmaster get-tasks --with-subtasks

# Get visual complexity report
taskmaster complexity-report --threshold 7
```

### Example 2: Managing Active Development

**Scenario:** You're actively developing and need to track progress

#### Via AI Assistant
```
You: "What should I work on next?"

AI Assistant: "Let me check TaskMaster for the next available task..."

[AI Assistant calls next_task() MCP tool]

AI Assistant: "TaskMaster recommends Task #3: 'Implement Excel Add-in UI'
since its dependencies (Task #1) are complete. This task has 6 subtasks
and a complexity score of 7/10."
```

#### Via CLI
```bash
# Find next task to work on
taskmaster next-task

# Get detailed task information
taskmaster get-task --id 3

# Update task status
taskmaster set-status --id 3.2 --status in-progress

# Generate individual task files for development
taskmaster generate
```

### Example 3: Project Analysis and Reporting

**Scenario:** You need project insights and visual reports

#### CLI Commands for Analysis
```bash
# Comprehensive complexity analysis
taskmaster analyze-complexity --threshold 6 --research

# Visual project overview
taskmaster get-tasks --status pending --complexity-report

# Dependency validation
taskmaster validate-dependencies

# Export project data
taskmaster get-tasks --format json > project-status.json
```

---

## 🛠️ TaskMaster MCP Tools Reference

### Core Project Management Tools

#### Project Initialization
- `initialize_project()` - Sets up .taskmaster directory structure
- `models()` - Configure AI models for task generation
- `parse_prd()` - Convert PRD documents into organized tasks

#### Task Management
- `get_tasks()` - Retrieve all tasks with filtering options
- `get_task()` - Get detailed information about specific task
- `add_task()` - Create new tasks with AI assistance
- `update_task()` - Modify existing task content
- `remove_task()` - Delete tasks permanently
- `set_task_status()` - Update task completion status

#### Subtask Operations
- `add_subtask()` - Add subtasks to existing tasks
- `update_subtask()` - Modify subtask content
- `remove_subtask()` - Delete or convert subtasks
- `expand_task()` - Break tasks into detailed subtasks
- `expand_all()` - Expand all pending tasks automatically

#### Dependency Management
- `add_dependency()` - Create task relationships
- `remove_dependency()` - Remove task dependencies
- `validate_dependencies()` - Check for circular references
- `fix_dependencies()` - Automatically resolve dependency issues

#### Analysis and Reporting
- `next_task()` - Find next available task to work on
- `analyze_project_complexity()` - Evaluate task difficulty
- `complexity_report()` - Generate detailed complexity analysis
- `generate()` - Create individual task files for development

### AI Integration Features

#### Model Configuration
- **Primary Models**: Gemini 2.5 Pro, DeepSeek Coder, Gemini Flash
- **Research Mode**: Enhanced analysis with Perplexity integration
- **Cost Optimization**: Intelligent routing to cost-efficient models
- **Fallback Support**: Automatic model switching on failures

#### Intelligent Task Generation
- **PRD Analysis**: Automatic task extraction from requirements
- **Complexity Scoring**: 1-10 scale for task difficulty assessment
- **Dependency Detection**: Automatic relationship identification
- **Expansion Recommendations**: Suggests when tasks need breakdown

### CLI Command Reference

#### Essential Commands
```bash
# Project setup
taskmaster init --project-root /path/to/project
taskmaster parse-prd --input prd.txt --num-tasks 15

# Task management
taskmaster get-tasks --status pending --with-subtasks
taskmaster next-task
taskmaster set-status --id 3.2 --status done

# Analysis and reporting
taskmaster complexity-report --threshold 7
taskmaster validate-dependencies
taskmaster generate
```

---

## 🧭 Best Practices and Workflows

### Project Setup Workflow

#### 1. Initial Setup
```bash
# Create project directory
mkdir my-project && cd my-project

# Initialize TaskMaster
taskmaster init --project-root $(pwd)

# Configure AI models (optional)
taskmaster models --set-main "gemini-2.5-pro" --set-research "sonar-pro"
```

#### 2. Requirements to Tasks
```bash
# Place PRD in .taskmaster/docs/prd.txt
# Then parse into tasks
taskmaster parse-prd --num-tasks 15 --research

# Review generated tasks
taskmaster get-tasks --with-subtasks
```

#### 3. Development Workflow
```bash
# Find next task
taskmaster next-task

# Work on task, update status
taskmaster set-status --id 3.1 --status in-progress

# Complete and move to next
taskmaster set-status --id 3.1 --status done
```

### Task Organization Best Practices

#### Task Complexity Guidelines
- **1-3**: Simple configuration or documentation tasks
- **4-6**: Standard development features
- **7-8**: Complex integrations or new systems
- **9-10**: Major architectural components

#### When to Expand Tasks
- Complexity score > 7
- Task description is vague or high-level
- Multiple developers need to work on different parts
- Task has unclear dependencies

#### Dependency Management
- Keep dependency chains short (max 3-4 levels)
- Validate dependencies regularly
- Use `taskmaster validate-dependencies` before major milestones
- Fix circular dependencies immediately

### Integration with Development Tools

#### Git Integration
```bash
# Create task-specific branches
git checkout -b task-$(taskmaster next-task --format id)

# Commit with task references
git commit -m "feat: implement task #3.2 - Excel UI components"
```

#### Documentation Generation
```bash
# Generate individual task files
taskmaster generate

# Create project documentation
taskmaster complexity-report --output docs/complexity.md
```

### Troubleshooting Common Issues

#### "Tasks seem too granular"
**Solution**: Use `taskmaster get-tasks --status pending` to see only actionable items. Completed subtasks are automatically hidden.

#### "Dependencies are confusing"
**Solution**: Run `taskmaster validate-dependencies` to check for issues. Use CLI visualization for dependency graphs.

#### "AI-generated tasks don't match my needs"
**Solution**: Use `taskmaster update-task --id X --prompt "new requirements"` to refine tasks with AI assistance.

#### "Project feels overwhelming"
**Solution**: Focus on `taskmaster next-task` - it shows only what you can work on right now based on dependencies.

---

## ❓ Frequently Asked Questions

### About TaskMaster Framework

**Q: Is TaskMaster a separate application I need to install?**
A: No, TaskMaster is a development framework/tool. You interact with it through AI assistants (MCP tools) or directly via CLI commands.

**Q: Do I need to learn command-line tools?**
A: Not necessarily. You can work entirely through AI assistants. CLI is available when you want visual reports or direct control.

**Q: How does TaskMaster integrate with my existing workflow?**
A: TaskMaster works alongside your development tools. It organizes tasks and tracks progress while you code in your preferred environment.

### About AI Integration

**Q: Which AI models work with TaskMaster?**
A: TaskMaster supports multiple providers: OpenAI, OpenRouter, Perplexity, and others. It intelligently routes tasks to cost-efficient models.

**Q: Can I use TaskMaster without AI assistance?**
A: Yes! All TaskMaster functions are available via CLI. AI assistance is optional but helpful for task generation and analysis.

**Q: How accurate is AI-generated task breakdown?**
A: TaskMaster uses complexity scoring and dependency analysis to create realistic task structures. You can always refine tasks manually.

### About Project Management

**Q: How does TaskMaster handle large projects?**
A: TaskMaster breaks projects into manageable tasks with dependency tracking. Use complexity analysis to identify tasks that need further breakdown.

**Q: Can multiple people work on the same TaskMaster project?**
A: Yes, TaskMaster stores project data in files that can be shared via Git or other version control systems.

**Q: What happens if I need to change project requirements?**
A: Use `update_task()` or CLI commands to modify tasks. TaskMaster can regenerate task structures based on new requirements.

---

## 🎯 Key Takeaways

### For Success with TaskMaster Framework:

1. **Start with clear requirements** - Use PRD parsing for best results
2. **Leverage both interfaces** - AI assistant for guidance, CLI for control
3. **Trust the dependency system** - Follow `next-task` recommendations
4. **Use complexity analysis** - Expand tasks with scores > 7
5. **Validate regularly** - Check dependencies and project health

### Remember:

- TaskMaster is a tool, not an application - it enhances your development workflow
- You have full control - use AI assistance when helpful, CLI when you need precision
- All project data is stored in files - easy to backup, share, and version control
- The framework adapts to your needs - modify tasks and structure as requirements evolve
- Success is measured by organized, trackable development progress

---

## 🔧 Technical Implementation Details

### MCP (Model Context Protocol) Integration

TaskMaster integrates with AI assistants through MCP tools, enabling programmatic project management.

**Communication Flow:**
```
You → AI Assistant:
"I want to add customer reviews to my restaurant app"

AI Assistant → TaskMaster (via MCP):
- add_task(prompt="Customer review system with 5-star ratings")
- analyze_complexity(ids="16")
- expand_task(id="16", num="6")

TaskMaster → AI Assistant (response):
- Task #16 created with 6 subtasks
- Complexity score: 6/10 (moderate)
- Dependencies: Tasks #2, #3 (database, UI framework)

AI Assistant → You:
"I've added Task #16 for customer reviews. TaskMaster generated 6 subtasks
with moderate complexity. It depends on completing the database and UI tasks first."
```

### CLI Architecture

#### Command Structure
```bash
taskmaster <command> [options]

# Core commands
init, parse-prd, get-tasks, add-task, update-task
set-status, next-task, complexity-report, generate

# Advanced commands
expand-task, validate-dependencies, analyze-complexity
add-dependency, remove-dependency, move-task
```

#### Data Storage
- **Project Root**: `.taskmaster/` directory
- **Tasks**: `tasks/tasks.json` with structured task data
- **Reports**: `reports/` for complexity and analysis outputs
- **Documentation**: `docs/` for PRDs and project documentation

### Integration Points

#### Version Control
- All TaskMaster files are Git-friendly JSON/Markdown
- Task IDs remain stable across updates
- Merge conflicts are rare due to structured data format

#### Development Tools
- Generate individual task files for IDE integration
- Export data in multiple formats (JSON, CSV, Markdown)
- CLI provides scriptable interface for automation

---

## 📊 Real-World Project Examples

### Example 1: Excella Excel Add-in Project
**Project Type**: AI-powered Excel add-in for data analysis
**TaskMaster Usage**: 16 main tasks, 83 subtasks generated from PRD
**Timeline**: 12-16 weeks estimated
**Key Features**: React UI, Office.js integration, AI orchestration, sandbox execution

**TaskMaster Structure:**
```
Task #1: Setup Project Repository (7 complexity, 6 subtasks)
Task #2: Configure Supabase Backend (8 complexity, 7 subtasks)
Task #3: Implement Excel Add-in UI (7 complexity, 6 subtasks)
Task #4: Integrate Office.js (8 complexity, 6 subtasks)
Task #5: Set Up AI Orchestration (9 complexity, 5 subtasks)
...
```

### Example 2: E-commerce Platform
**Project Type**: Multi-vendor marketplace
**TaskMaster Usage**: 22 main tasks, 95 subtasks
**Timeline**: 20-24 weeks estimated
**Key Features**: Vendor management, payment processing, inventory tracking

**Complexity Distribution:**
- Simple tasks (1-3): 15% (documentation, configuration)
- Medium tasks (4-6): 45% (standard features)
- Complex tasks (7-8): 30% (integrations)
- Major tasks (9-10): 10% (core architecture)

### Example 3: Mobile App with Backend
**Project Type**: Social fitness tracking app
**TaskMaster Usage**: 18 main tasks, 67 subtasks
**Timeline**: 16-20 weeks estimated
**Key Features**: React Native, real-time sync, social features

**Dependency Chain Example:**
```
Task #1: Project Setup → Task #2: Database Design → Task #5: API Development
                     → Task #3: Mobile UI → Task #8: Social Features
```

---

## 🛠️ Advanced TaskMaster Features

### Intelligent Task Analysis

#### Complexity Scoring Algorithm
TaskMaster evaluates tasks based on:
- **Technical Difficulty**: New technologies, complex integrations
- **Scope Size**: Lines of code, number of components
- **Dependency Count**: How many other tasks are affected
- **Risk Factors**: External APIs, security requirements

#### Automatic Expansion Recommendations
```bash
# Analyze project and recommend expansions
taskmaster analyze-complexity --threshold 7

# Output example:
Task #5 (complexity: 9) - Recommend expanding into 5-7 subtasks
Task #12 (complexity: 8) - Consider breaking down payment integration
Task #18 (complexity: 7) - At threshold, review for expansion
```

### Multi-Project Coordination

#### Project Portfolio Management
```bash
# Initialize multiple projects
taskmaster init --project-root /projects/excella
taskmaster init --project-root /projects/mobile-app

# Cross-project reporting
taskmaster portfolio-status --projects /projects/*
```

#### Shared Component Tracking
- Identify reusable components across projects
- Track shared dependencies and updates
- Coordinate releases across multiple projects

### Advanced CLI Features

#### Visual Reports and Charts
```bash
# Generate visual complexity distribution
taskmaster complexity-report --chart --output complexity.png

# Dependency graph visualization
taskmaster dependency-graph --format svg

# Progress timeline
taskmaster timeline --weeks 16 --format html
```

#### Automation and Scripting
```bash
# Automated daily standup report
taskmaster daily-report --format slack --webhook $SLACK_URL

# Git integration
taskmaster git-hooks --install  # Auto-update task status from commits

# Export for external tools
taskmaster export --format jira --output tasks.xml
```

---

## 🎨 TaskMaster Output and Documentation

### Generated Project Structure

TaskMaster creates organized project documentation:

#### Directory Structure
```
project-root/
├── .taskmaster/
│   ├── tasks/
│   │   ├── tasks.json          # Main task database
│   │   ├── task-001.md         # Individual task files
│   │   ├── task-002.md
│   │   └── ...
│   ├── reports/
│   │   ├── complexity-report.json
│   │   └── dependency-analysis.json
│   └── docs/
│       ├── prd.txt             # Original requirements
│       └── project-summary.md
```

#### Task File Format
```markdown
# Task #3: Implement Excel Add-in UI

**Status**: pending
**Priority**: high
**Complexity**: 7/10
**Dependencies**: Task #1

## Description
Build the Excel add-in sidebar UI using React 19, Shadcn/ui, and Tailwind CSS...

## Subtasks
- [ ] 3.1: Set up React Excel add-in project
- [ ] 3.2: Integrate Tailwind CSS
- [ ] 3.3: Implement Shadcn/ui components
...

## Implementation Notes
- Use 320px taskpane width optimization
- Follow Apple-inspired design principles
- Integrate voice input capabilities
```

### Reporting and Analytics

#### Complexity Analysis Report
```json
{
  "projectSummary": {
    "totalTasks": 16,
    "averageComplexity": 6.8,
    "highComplexityTasks": 4,
    "recommendedExpansions": 3
  },
  "taskAnalysis": [
    {
      "id": 5,
      "title": "Set Up AI Orchestration",
      "complexity": 9,
      "recommendation": "Expand into 5-7 subtasks",
      "reasoning": "Multiple AI model integrations require detailed breakdown"
    }
  ]
}
```

#### Progress Tracking
- **Completion Percentage**: Overall and per-task progress
- **Velocity Metrics**: Tasks completed per week/sprint
- **Dependency Health**: Blocked vs. available tasks
- **Risk Assessment**: High-complexity tasks requiring attention

---

## 📈 Development Efficiency Gains

### Productivity Improvements

**Traditional Project Management vs. TaskMaster Framework:**

| Aspect | Traditional | TaskMaster | Improvement |
|--------|-------------|------------|-------------|
| Task Planning | Manual breakdown | AI-assisted generation | 70% faster |
| Dependency Tracking | Spreadsheets/tools | Automatic validation | 85% fewer errors |
| Progress Visibility | Weekly meetings | Real-time CLI/reports | Daily insights |
| Requirement Changes | Manual re-planning | AI-powered updates | 60% faster adaptation |

### Development Benefits

#### For Solo Developers
- **Structured Approach**: Clear next steps without overwhelm
- **AI Assistance**: Intelligent task generation and analysis
- **Progress Tracking**: Visual confirmation of advancement
- **Scope Management**: Complexity scoring prevents scope creep

#### For Small Teams
- **Shared Understanding**: Common task structure and terminology
- **Parallel Work**: Clear dependency management enables concurrent development
- **Knowledge Transfer**: Documented task breakdown preserves team knowledge
- **Quality Assurance**: Complexity analysis identifies high-risk areas

### Success Metrics

#### Project Health Indicators
- **Task Completion Velocity**: Average tasks completed per time period
- **Dependency Resolution Rate**: How quickly blocked tasks become available
- **Complexity Distribution**: Balance of simple vs. complex tasks
- **Scope Stability**: Frequency of major task restructuring

#### Quality Metrics
- **Requirement Traceability**: Tasks linked back to original requirements
- **Test Coverage**: Subtasks include testing and validation steps
- **Documentation Completeness**: All tasks have clear implementation details
- **Risk Mitigation**: High-complexity tasks identified and managed early

---

## 🔮 TaskMaster Roadmap and Evolution

### Current Version (0.16.1)

#### Core Features Available Now
- **MCP Integration**: Full AI assistant integration via Model Context Protocol
- **CLI Interface**: Complete command-line access for all operations
- **Task Management**: Create, update, track, and organize development tasks
- **Dependency System**: Automatic validation and relationship management
- **Complexity Analysis**: AI-powered task difficulty assessment
- **PRD Parsing**: Convert requirements documents into structured tasks
- **Multi-Model Support**: OpenAI, OpenRouter, Perplexity integration

### Planned Enhancements (2025)

#### Enhanced AI Capabilities
- **Improved PRD Analysis**: Better natural language understanding for requirements
- **Predictive Task Generation**: AI suggests tasks based on project patterns
- **Automated Testing Tasks**: Generate test scenarios and validation steps
- **Code Integration**: Direct integration with development environments

#### Advanced CLI Features
- **Interactive Dashboard**: Terminal-based visual project overview
- **Real-time Collaboration**: Multi-developer project coordination
- **Advanced Reporting**: Custom report generation and export formats
- **Plugin System**: Extensible architecture for custom integrations

#### Integration Ecosystem
- **IDE Plugins**: VSCode, IntelliJ, and other editor integrations
- **CI/CD Integration**: Automatic task status updates from build systems
- **Project Templates**: Industry-specific starting templates
- **API Ecosystem**: RESTful API for custom tool integration

### Long-term Vision

#### Framework Evolution
- **Language Agnostic**: Support for any programming language or framework
- **Enterprise Features**: Multi-project portfolios, team management, reporting
- **AI Model Marketplace**: Choose from specialized models for different task types
- **Community Templates**: Shared task templates and best practices

#### Developer Experience
- **Zero Configuration**: Automatic project detection and setup
- **Intelligent Defaults**: Smart recommendations based on project context
- **Learning System**: Framework improves recommendations based on usage patterns
- **Documentation Generation**: Automatic project documentation from task structure

---

## 📚 Additional Resources

### Getting Started
- **Installation Guide**: Setup instructions for different environments
- **Quick Start Tutorial**: 15-minute introduction to core concepts
- **Example Projects**: Sample TaskMaster projects for different domains
- **Video Tutorials**: Step-by-step visual guides

### Advanced Usage
- **MCP Tool Reference**: Complete API documentation for AI integration
- **CLI Command Reference**: Detailed documentation for all commands
- **Best Practices Guide**: Proven patterns for project organization
- **Troubleshooting Guide**: Common issues and solutions

### Community and Support
- **GitHub Repository**: Source code, issues, and contributions
- **Documentation Site**: Comprehensive guides and tutorials
- **Community Forum**: User discussions and shared experiences
- **Developer Blog**: Updates, tips, and case studies

---

*TaskMaster is a development framework that enhances your software development process through intelligent task organization, AI assistance, and comprehensive project tracking. It's designed to work with your existing tools and workflow, not replace them.*
