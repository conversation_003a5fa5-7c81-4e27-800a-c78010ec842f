# Package Installation Guide
*Excella MVP Technology Stack Setup - December 2024*

## Overview

This guide provides step-by-step installation instructions for all packages in the Excella MVP technology stack. All versions are validated for compatibility with React 19.0.0 and the latest stable releases.

### 📋 **Prerequisites**
- **Node.js**: 18.17+ or 20.x (LTS recommended)
- **Python**: 3.11+ or 3.12 (for AI microservices)
- **Git**: Latest version
- **VS Code**: Recommended IDE with Office Add-in extensions

---

## Frontend Package Installation

### 1. Initialize Project Structure

```bash
# Create project directory
mkdir excella-mvp
cd excella-mvp

# Initialize package.json
npm init -y

# Create project structure
mkdir -p src/{components,pages,hooks,utils,types}
mkdir -p public/{icons,images}
mkdir -p docs
mkdir -p .excella/{core,research,setup,specs,assets}
```

### 2. Core Frontend Dependencies

```bash
# React 19 with TypeScript
npm install react@^19.0.0 react-dom@^19.0.0
npm install --save-dev typescript@^5.6.0
npm install --save-dev @types/react@^19.0.0 @types/react-dom@^19.0.0

# Next.js 15 (Required for React 19)
npm install next@^15.0.0
npm install --save-dev @types/node@^20.0.0

# State Management
npm install zustand@^5.0.5
```

### 3. UI Framework & Styling

```bash
# Tailwind CSS (Recommended over Fluent UI for React 19)
npm install --save-dev tailwindcss@^3.4.0 postcss@^8.4.0 autoprefixer@^10.4.0
npx tailwindcss init -p

# Shadcn/ui Components (Core functional components)
npx shadcn-ui@latest init
npx shadcn-ui@latest add button card input

# Framer Motion (Required for Magic UI animations)
npm install framer-motion@latest

# Magic UI Components (Copy-paste animated components)
# Note: Magic UI components are copied directly from https://magicui.design/
# No npm package installation required - follow copy-paste philosophy

# Alternative: Fluent UI (React 19 incompatible - not recommended)
# npm install @fluentui/react-components@^9.64.0
```

### 4. Data Visualization & Interactive Components (Task 1.2)

```bash
# Core visualization libraries for AI results display
npm install react-plotly.js@^2.6.0 plotly.js@^2.35.0

# Advanced data tables with sorting, filtering, pagination
npm install @tanstack/react-table@^8.20.0

# Code syntax highlighting for execution results
npm install prism-react-renderer@^2.4.0 prismjs@^1.29.0

# Excel file operations and downloads
npm install exceljs@^4.4.0 file-saver@^2.0.5

# Type definitions
npm install --save-dev @types/file-saver@^2.0.7 @types/prismjs@^1.26.4
```

### 5. Performance Optimization Libraries

```bash
# Virtual scrolling for large datasets (320px taskpane optimization)
npm install react-window@^1.8.8

# Input debouncing for Excel add-in performance
npm install lodash.debounce@^4.0.8

# Optional: Advanced virtualization for complex data tables
npm install @tanstack/react-virtual@^3.10.0

# Type definitions
npm install --save-dev @types/lodash.debounce@^4.0.9
```

### 6. Build Tools & Development

```bash
# Webpack & Development Server
npm install --save-dev webpack@^5.96.0 webpack-cli@^5.1.0
npm install --save-dev webpack-dev-server@^5.2.0
npm install --save-dev ts-loader@^9.5.2
npm install --save-dev html-webpack-plugin@^5.6.0

# Office.js for Excel Add-in
npm install office-addin-manifest@^1.13.0
npm install --save-dev @types/office-js@^1.0.0
```

### 7. Testing Framework

```bash
# Vitest & React Testing Library
npm install --save-dev vitest@^3.1.4
npm install --save-dev @testing-library/react@^16.3.0
npm install --save-dev @testing-library/dom@^10.0.0
npm install --save-dev @testing-library/jest-dom@^6.0.0

# Playwright for E2E Testing
npm install --save-dev @playwright/test@^1.52.0
npx playwright install --with-deps

# Storybook for Component Documentation
npm install --save-dev storybook@^9.0.3 @storybook/react@^9.0.3
npx storybook@latest init
```

### 8. Code Quality Tools

```bash
# ESLint & TypeScript ESLint
npm install --save-dev eslint@^9.27.0
npm install --save-dev @typescript-eslint/eslint-plugin@^8.0.0
npm install --save-dev @typescript-eslint/parser@^8.0.0

# Prettier & Husky
npm install --save-dev prettier@^3.5.3
npm install --save-dev husky@^9.0.0
npx husky init

# Initialize ESLint
npx eslint --init
```

---

## Backend Package Installation

### 1. Supabase & Database

```bash
# Supabase Client & SSR (Replaces deprecated auth-helpers)
npm install @supabase/supabase-js@^2.0.0
npm install @supabase/ssr@latest

# Database Connectors
npm install pg@^8.8.0                    # PostgreSQL
npm install @types/pg@^8.10.9
npm install mysql2@^3.6.0                # MySQL/MariaDB
npm install @types/mysql2@^3.0.0
npm install mssql@^11.0.0                 # Microsoft SQL Server
npm install @types/mssql@^9.1.5
npm install oracledb@^6.0.0              # Oracle Database
npm install @types/oracledb@^6.0.1
npm install snowflake-sdk@^1.12.0        # Snowflake Data Warehouse

# Business System APIs
npm install jsforce@^2.0.0               # Salesforce CRM
npm install @types/jsforce@^2.0.0
npm install zoho-crm-api@^2.1.0          # Zoho CRM
npm install node-quickbooks@^3.0.0       # QuickBooks Online

# Advanced AI Integration (Phase 2 - Enhancement)
# Model Context Protocol (MCP)
npm install @modelcontextprotocol/sdk@^1.0.0    # MCP SDK
npm install @modelcontextprotocol/client@^1.0.0 # MCP Client
npm install @modelcontextprotocol/server@^1.0.0 # MCP Server

# AG-UI Protocol (Agent-User Interaction)
npm install @ag-ui/core@^1.0.0                  # AG-UI Core Protocol
npm install @ag-ui/react@^1.0.0                 # AG-UI React Client
npm install @ag-ui/typescript-sdk@^1.0.0        # AG-UI TypeScript SDK

# DO NOT INSTALL (Deprecated):
# npm install @supabase/auth-helpers-nextjs
```

### 2. tRPC & API Layer

```bash
# tRPC 11.x (Major upgrade from 10.x)
npm install @trpc/server@^11.2.0
npm install @trpc/client@^11.2.0
npm install @trpc/next@^11.2.0
npm install @trpc/react-query@^11.2.0

# Required tRPC Dependencies
npm install @tanstack/react-query@latest
npm install zod@latest
```

### 3. Python AI Microservices Setup

```bash
# Create Python virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Upgrade pip
python -m pip install --upgrade pip
```

### 4. AI Framework & Core Libraries

```bash
# Agno AI Framework (formerly Phidata)
pip install agno>=1.2.0

# FastAPI & Server
pip install fastapi==0.115.*
pip install uvicorn[standard]==0.24.*

# Core dependencies (auto-installed but listed for reference)
pip install pydantic>=2.0.0
pip install starlette>=0.40.0
```

### 5. Data Processing Libraries

```bash
# Core data science stack
pip install pandas>=2.2.0
pip install numpy>=2.1.0
pip install scipy>=1.11.0
pip install scikit-learn>=1.3.0

# NLP & ML Libraries
pip install spacy==3.8.6  # Use exact version
pip install nltk>=3.8.0
pip install transformers>=4.35.0

# Optional: spaCy transformer integration
pip install spacy-transformers
```

### 6. AI Service Providers

```bash
# Primary AI providers
pip install openai>=1.82.0
pip install python-open-router>=0.2.0
pip install google-cloud-aiplatform>=1.95.0

# Optional: Additional AI integrations
pip install google-genai
```

### 7. Visualization Libraries

```bash
# Visualization stack
pip install matplotlib>=3.10.0
pip install seaborn>=0.13.0
pip install plotly>=6.1.0

# Optional: Enhanced plotly functionality
pip install plotly[express]
```

---

## Monitoring & Analytics

### 1. Analytics & Error Tracking

```bash
# PostHog Analytics
npm install posthog-js@^1.90.0

# Sentry Error Tracking
npm install @sentry/nextjs@^9.24.0
npx @sentry/wizard@latest -i nextjs

# Upstash Redis & Rate Limiting
npm install @upstash/ratelimit@^0.4.0
npm install @upstash/redis@^1.34.0

# OpenReplay Session Replay
npm install @openreplay/tracker@^16.2.0
```

---

## Sandbox & Security

### 1. Code Execution Environment

```bash
# Pyodide for client-side Python execution
npm install pyodide@latest

# E2B Code Interpreter SDK
npm install @e2b/code-interpreter@latest
```

---

## Development Environment Setup

### 1. VS Code Extensions

Install these extensions for optimal development experience:

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "ms-office.office-addin-debugger",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-python.python",
    "ms-python.flake8",
    "ms-playwright.playwright"
  ]
}
```

### 2. Configuration Files

Create these configuration files in your project root:

#### `tsconfig.json`
```json
{
  "compilerOptions": {
    "strict": true,
    "target": "ES2022",
    "lib": ["DOM", "ES2022", "DOM.Iterable"],
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "incremental": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}
```

#### `tailwind.config.js`
```javascript
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {}
  },
  plugins: []
}
```

#### `next.config.js`
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true
  },
  typescript: {
    tsconfigPath: './tsconfig.json'
  }
}

module.exports = nextConfig
```

### 3. Python Requirements File

Create `requirements.txt`:

```txt
# AI Framework
agno>=1.2.0
fastapi==0.115.*
uvicorn[standard]==0.24.*

# Data Processing
pandas>=2.2.0
numpy>=2.1.0
scipy>=1.11.0
scikit-learn>=1.3.0

# NLP & ML
spacy==3.8.6
nltk>=3.8.0
transformers>=4.35.0

# AI Providers
openai>=1.82.0
python-open-router>=0.2.0
google-cloud-aiplatform>=1.95.0

# Visualization
matplotlib>=3.10.0
seaborn>=0.13.0
plotly>=6.1.0
```

---

## Installation Scripts

### 1. Complete Frontend Setup Script

Create `setup-frontend.sh`:

```bash
#!/bin/bash
echo "Setting up Excella Frontend..."

# Install Node.js dependencies
npm install react@^19.0.0 react-dom@^19.0.0
npm install next@^15.0.0
npm install zustand@^5.0.5

# Development dependencies
npm install --save-dev typescript@^5.6.0
npm install --save-dev @types/react@^19.0.0 @types/react-dom@^19.0.0
npm install --save-dev @types/node@^20.0.0

# Build tools
npm install --save-dev webpack@^5.96.0 webpack-cli@^5.1.0
npm install --save-dev webpack-dev-server@^5.2.0
npm install --save-dev ts-loader@^9.5.2

# UI Framework
npm install --save-dev tailwindcss@^3.4.0 postcss@^8.4.0 autoprefixer@^10.4.0
npm install framer-motion@latest
npx tailwindcss init -p

# Data Visualization & Interactive Components (Task 1.2)
npm install react-plotly.js@^2.6.0 plotly.js@^2.35.0
npm install @tanstack/react-table@^8.20.0
npm install prism-react-renderer@^2.4.0 prismjs@^1.29.0
npm install exceljs@^4.4.0 file-saver@^2.0.5
npm install --save-dev @types/file-saver@^2.0.7 @types/prismjs@^1.26.4

# Performance Optimization
npm install react-window@^1.8.8 lodash.debounce@^4.0.8
npm install --save-dev @types/lodash.debounce@^4.0.9

# Testing
npm install --save-dev vitest@^3.1.4
npm install --save-dev @testing-library/react@^16.3.0
npm install --save-dev @playwright/test@^1.52.0

# Code quality
npm install --save-dev eslint@^9.27.0 prettier@^3.5.3 husky@^9.0.0

echo "Frontend setup complete!"
echo "Note: Magic UI components should be copied from https://magicui.design/ as needed"
echo "Task 1.2 libraries installed: react-plotly.js, @tanstack/react-table, prism-react-renderer, exceljs, file-saver"
```

### 2. Magic UI Component Setup

Since Magic UI follows a copy-paste philosophy, create a dedicated directory structure:

```bash
# Create Magic UI component directory
mkdir -p src/components/magic-ui

# Example: Copy Magic UI components as needed
# 1. Visit https://magicui.design/
# 2. Browse components (e.g., animated-beam, confetti, particles)
# 3. Copy component code to src/components/magic-ui/
# 4. Import and use in your components

# Example directory structure:
# src/components/magic-ui/
# ├── animated-beam.tsx
# ├── confetti.tsx
# ├── particles.tsx
# ├── blur-fade.tsx
# └── border-beam.tsx
```

### 3. Complete Backend Setup Script

Create `setup-backend.sh`:

```bash
#!/bin/bash
echo "Setting up Excella Backend..."

# Supabase & Database Connectors (Phase 1 - MVP)
npm install @supabase/supabase-js@^2.0.0 @supabase/ssr@latest
npm install pg@^8.8.0 mysql2@^3.6.0 mssql@^11.0.0 oracledb@^6.0.0 snowflake-sdk@^1.12.0
npm install jsforce@^2.0.0 zoho-crm-api@^2.1.0 node-quickbooks@^3.0.0

# Advanced AI Integration (Phase 2 - Enhancement)
npm install @modelcontextprotocol/sdk@^1.0.0 @modelcontextprotocol/client@^1.0.0
npm install @ag-ui/core@^1.0.0 @ag-ui/react@^1.0.0 @ag-ui/typescript-sdk@^1.0.0

# tRPC & API Layer
npm install @trpc/server@^11.2.0 @trpc/client@^11.2.0
npm install @tanstack/react-query@latest zod@latest

# Python virtual environment
python -m venv venv
source venv/bin/activate  # Use venv\Scripts\activate on Windows

# Python dependencies
pip install --upgrade pip
pip install -r requirements.txt

echo "Backend setup complete!"
```

---

## Verification & Testing

### 1. Verify Installation

```bash
# Check Node.js versions
node --version  # Should be 18.17+ or 20.x
npm --version

# Check Python versions
python --version  # Should be 3.11+ or 3.12
pip --version

# Verify React 19 installation
npm list react react-dom

# Verify TypeScript
npx tsc --version

# Test build
npm run build
```

### 2. Run Development Servers

```bash
# Frontend development server
npm run dev

# Python FastAPI server
cd backend
uvicorn main:app --reload --port 8000

# Run tests
npm test
npm run test:e2e
```

---

## Troubleshooting

### Common Issues

1. **React 19 Compatibility Issues**
   - Ensure Next.js is version 15.x or higher
   - Update all React-related packages to latest versions

2. **tRPC 11.x Migration Issues**
   - Follow official migration guide from tRPC 10.x
   - Update client setup and API structure

3. **Python Package Conflicts**
   - Use exact version for spaCy (3.8.6)
   - Ensure Python 3.11+ for all AI libraries

4. **Office.js Development Issues**
   - Enable HTTPS in webpack-dev-server
   - Configure proper CORS headers

### Support Resources

- **React 19 Documentation**: https://react.dev/
- **Next.js 15 Guide**: https://nextjs.org/docs
- **tRPC 11.x Migration**: https://trpc.io/docs/migrate-from-v10-to-v11
- **Supabase SSR Guide**: https://supabase.com/docs/guides/auth/server-side-rendering

This installation guide ensures a smooth setup process for the complete Excella MVP technology stack.
