-- Excella Database Migration Scripts
-- Supabase PostgreSQL 15.x with Row Level Security
-- Updated for Wallet-Based Payment Model (December 2024)

-- =====================================================
-- CORE TABLES: User Management & Authentication
-- =====================================================

-- Users table with wallet-based payment tracking and African market support
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Wallet Information (Replaces Subscription Model)
  wallet_balance_cents INTEGER DEFAULT 500, -- Default $5.00 starting balance
  total_spent_cents INTEGER DEFAULT 0,
  total_loaded_cents INTEGER DEFAULT 500, -- Track total money loaded
  low_balance_threshold_cents INTEGER DEFAULT 100, -- $1.00 warning threshold
  auto_reload_enabled BOOLEAN DEFAULT FALSE,
  auto_reload_amount_cents INTEGER DEFAULT 500, -- $5.00 default reload

  -- Usage Tracking (Real-time cost tracking)
  total_queries_count INTEGER DEFAULT 0,
  total_tokens_used INTEGER DEFAULT 0,
  last_query_at TIMESTAMPTZ,

  -- Regional Settings (African Market Focus)
  currency TEXT DEFAULT 'USD' CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),
  timezone TEXT DEFAULT 'UTC',
  language TEXT DEFAULT 'en' CHECK (language IN ('en', 'fr')),

  -- Platform Preferences
  preferred_platform TEXT DEFAULT 'excel' CHECK (preferred_platform IN ('excel', 'web')),
  onboarding_completed BOOLEAN DEFAULT FALSE,

  -- Cost Transparency Settings
  show_cost_estimates BOOLEAN DEFAULT TRUE, -- User preference for cost visibility
  cost_alert_threshold_cents INTEGER DEFAULT 50, -- Alert when query costs > $0.50

  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
  CONSTRAINT positive_wallet_balance CHECK (wallet_balance_cents >= 0)
);

-- Teams table for collaborative features (volume pricing, admin controls)
CREATE TABLE IF NOT EXISTS teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Team Wallet & Volume Pricing
  shared_wallet_balance_cents INTEGER DEFAULT 0, -- Optional shared team wallet
  volume_discount_percent INTEGER DEFAULT 0, -- Volume pricing discount (0-30%)
  max_members INTEGER DEFAULT 5,
  current_members INTEGER DEFAULT 1,

  -- Team Settings
  shared_templates JSONB DEFAULT '[]',
  team_preferences JSONB DEFAULT '{}',
  admin_controls JSONB DEFAULT '{"cost_limits": false, "usage_analytics": true}',

  -- Usage Analytics
  total_team_spent_cents INTEGER DEFAULT 0,
  total_team_queries INTEGER DEFAULT 0,

  CONSTRAINT min_team_size CHECK (max_members >= 5) -- Minimum 5 users for Team features
);

-- Team Members Junction Table
CREATE TABLE IF NOT EXISTS team_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(team_id, user_id)
);

-- =====================================================
-- AI CONVERSATION SYSTEM
-- =====================================================

-- AI conversation sessions
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Conversation Context
  platform TEXT NOT NULL CHECK (platform IN ('excel', 'web')),
  excel_workbook_id TEXT, -- Excel workbook identifier
  data_sources JSONB DEFAULT '[]', -- Connected databases/files
  
  -- AI Configuration
  agent_mode TEXT DEFAULT 'chat' CHECK (agent_mode IN ('chat', 'agent')),
  active_agents JSONB DEFAULT '["DataAnalyst"]',
  
  -- Status
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
  message_count INTEGER DEFAULT 0,
  
  -- Performance Tracking
  total_tokens_used INTEGER DEFAULT 0,
  total_cost_usd DECIMAL(10,4) DEFAULT 0.0000
);

-- Conversation Messages with Excella AI model stack
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Message Content
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'analysis', 'visualization', 'code', 'error')),
  
  -- AI Metadata (Excella Model Stack)
  agent_name TEXT, -- 'DataAnalyst', 'Visualizer', 'CodeExecutor'
  model_used TEXT, -- 'gemini-2.5-pro', 'claude-4-sonnet', 'deepseek-coder'
  tokens_used INTEGER DEFAULT 0,
  cost_usd DECIMAL(8,4) DEFAULT 0.0000,
  execution_time_ms INTEGER,
  confidence_score DECIMAL(3,2),
  sandbox_method TEXT CHECK (sandbox_method IN ('pyodide', 'e2b', 'hybrid')), -- Execution method used
  
  -- Results Data
  result_data JSONB, -- Analysis results, charts, code output
  attachments JSONB DEFAULT '[]', -- File attachments, images
  
  -- User Interaction
  feedback TEXT CHECK (feedback IN ('positive', 'negative', 'neutral')),
  feedback_comment TEXT,
  regenerated_from UUID REFERENCES messages(id)
);

-- =====================================================
-- DATABASE CONNECTIVITY
-- =====================================================

-- User's database and data source connections
CREATE TABLE IF NOT EXISTS data_connections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Connection Details
  name TEXT NOT NULL, -- User-friendly name
  connection_type TEXT NOT NULL CHECK (connection_type IN (
    'postgresql', 'mysql', 'mssql', 'oracle', 'supabase',
    'salesforce', 'zoho', 'quickbooks', 'snowflake',
    'onedrive', 'googledrive', 'googlesheets', 'local_file'
  )),
  
  -- Connection Configuration (encrypted)
  connection_config JSONB NOT NULL, -- Encrypted connection details
  
  -- Status & Health
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'error')),
  last_tested TIMESTAMPTZ,
  last_used TIMESTAMPTZ,
  error_message TEXT,
  
  -- Usage Statistics
  query_count INTEGER DEFAULT 0,
  data_volume_mb DECIMAL(10,2) DEFAULT 0.00,
  
  -- Access Method (Phase 1: Direct, Phase 2: MCP)
  access_method TEXT DEFAULT 'direct' CHECK (access_method IN ('direct', 'mcp', 'hybrid'))
);

-- =====================================================
-- WALLET-BASED PAYMENT SYSTEM
-- =====================================================

-- Wallet transactions for all money movements (loads, spends, refunds)
CREATE TABLE IF NOT EXISTS wallet_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  team_id UUID REFERENCES teams(id) ON DELETE SET NULL, -- For team wallet transactions
  created_at TIMESTAMPTZ DEFAULT NOW(),

  -- Transaction Details
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('load', 'spend', 'refund', 'bonus', 'team_transfer')),
  amount_cents INTEGER NOT NULL, -- Positive for credits, negative for debits
  balance_after_cents INTEGER NOT NULL, -- Wallet balance after this transaction

  -- Transaction Context
  description TEXT NOT NULL, -- Human-readable description
  reference_id TEXT, -- External payment reference or internal query ID

  -- AI Query Context (for 'spend' transactions)
  conversation_id UUID REFERENCES conversations(id) ON DELETE SET NULL,
  message_id UUID REFERENCES messages(id) ON DELETE SET NULL,
  model_used TEXT, -- AI model that generated the cost
  tokens_used INTEGER DEFAULT 0,
  execution_time_ms INTEGER,

  -- Payment Provider (for 'load' transactions)
  provider TEXT CHECK (provider IN ('paystack', 'flutterwave', 'stripe', 'manual')),
  provider_transaction_id TEXT,
  provider_fee_cents INTEGER DEFAULT 0,

  -- Regional Settings
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),
  exchange_rate DECIMAL(10,6) DEFAULT 1.0, -- For non-USD currencies

  -- Status
  status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  failure_reason TEXT,

  CONSTRAINT valid_amount CHECK (
    (transaction_type = 'spend' AND amount_cents < 0) OR
    (transaction_type IN ('load', 'refund', 'bonus', 'team_transfer') AND amount_cents > 0)
  )
);

-- Payment methods for wallet loading (stored payment methods)
CREATE TABLE IF NOT EXISTS payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Payment Method Details
  provider TEXT NOT NULL CHECK (provider IN ('paystack', 'flutterwave', 'stripe')),
  provider_customer_id TEXT NOT NULL,
  provider_payment_method_id TEXT NOT NULL,

  -- Method Information
  method_type TEXT NOT NULL CHECK (method_type IN ('card', 'bank_account', 'mobile_money')),
  last_four TEXT, -- Last 4 digits of card or account
  brand TEXT, -- Visa, Mastercard, MTN, etc.
  expiry_month INTEGER,
  expiry_year INTEGER,

  -- Status
  is_default BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,

  -- Regional Support
  country_code TEXT, -- GH, NG, KE, etc.
  currency TEXT NOT NULL CHECK (currency IN ('USD', 'GHS', 'NGN', 'XOF', 'KES', 'UGX', 'TZS')),

  UNIQUE(provider, provider_payment_method_id)
);

-- Affiliate Program
CREATE TABLE IF NOT EXISTS affiliates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Affiliate Details
  referral_code TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
  
  -- Commission Tracking
  total_referrals INTEGER DEFAULT 0,
  total_commission_cents INTEGER DEFAULT 0,
  pending_commission_cents INTEGER DEFAULT 0,
  paid_commission_cents INTEGER DEFAULT 0,
  
  -- Payment Information
  payment_method JSONB, -- Bank details, mobile money, etc.
  last_payout_at TIMESTAMPTZ
);

-- Affiliate Commissions (Wallet-Based Model)
CREATE TABLE IF NOT EXISTS affiliate_commissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  affiliate_id UUID REFERENCES affiliates(id) ON DELETE CASCADE,
  wallet_transaction_id UUID REFERENCES wallet_transactions(id) ON DELETE CASCADE,
  referred_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),

  -- Commission Details
  commission_cents INTEGER NOT NULL,
  commission_type TEXT NOT NULL CHECK (commission_type IN ('initial_load', 'recurring_load', 'volume_bonus', 'performance_bonus')),
  original_load_amount_cents INTEGER NOT NULL, -- Original wallet load amount that triggered commission
  load_number INTEGER DEFAULT 1, -- Track if this is 1st, 2nd, 3rd load for this user
  commission_rate DECIMAL(5,2) NOT NULL, -- Commission percentage (e.g., 10.00 = 10%)

  -- Status & Payment
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'cancelled')),
  paid_at TIMESTAMPTZ,
  payment_reference TEXT,
  payout_transaction_id UUID REFERENCES wallet_transactions(id) ON DELETE SET NULL,

  -- Performance Tracking
  is_performance_bonus BOOLEAN DEFAULT FALSE,
  performance_tier TEXT CHECK (performance_tier IN ('bronze', 'silver', 'gold', 'platinum'))
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_wallet_balance ON users(wallet_balance_cents);
CREATE INDEX IF NOT EXISTS idx_users_currency ON users(currency);
CREATE INDEX IF NOT EXISTS idx_users_last_query_at ON users(last_query_at);

-- Team indexes
CREATE INDEX IF NOT EXISTS idx_teams_owner_id ON teams(owner_id);
CREATE INDEX IF NOT EXISTS idx_teams_volume_discount ON teams(volume_discount_percent);

-- Conversation indexes
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_platform ON conversations(platform);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);

-- Message indexes
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_model_used ON messages(model_used);
CREATE INDEX IF NOT EXISTS idx_messages_sandbox_method ON messages(sandbox_method);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

-- Wallet transaction indexes
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status ON wallet_transactions(status);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_created_at ON wallet_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_provider ON wallet_transactions(provider);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_conversation_id ON wallet_transactions(conversation_id);

-- Payment method indexes
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_provider ON payment_methods(provider);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_default ON payment_methods(is_default);

-- Affiliate indexes
CREATE INDEX IF NOT EXISTS idx_affiliates_referral_code ON affiliates(referral_code);
CREATE INDEX IF NOT EXISTS idx_affiliate_commissions_affiliate_id ON affiliate_commissions(affiliate_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_commissions_wallet_transaction_id ON affiliate_commissions(wallet_transaction_id);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliates ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliate_commissions ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own data" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Teams policies
CREATE POLICY "Team owners can manage teams" ON teams FOR ALL USING (auth.uid() = owner_id);
CREATE POLICY "Team members can view teams" ON teams FOR SELECT USING (
  auth.uid() = owner_id OR
  auth.uid() IN (SELECT user_id FROM team_members WHERE team_id = teams.id)
);

-- Team members policies
CREATE POLICY "Team owners can manage members" ON team_members FOR ALL USING (
  auth.uid() IN (SELECT owner_id FROM teams WHERE id = team_members.team_id)
);
CREATE POLICY "Team members can view members" ON team_members FOR SELECT USING (
  auth.uid() = user_id OR
  auth.uid() IN (SELECT owner_id FROM teams WHERE id = team_members.team_id)
);

-- Conversations policies
CREATE POLICY "Users can manage own conversations" ON conversations FOR ALL USING (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Users can manage own messages" ON messages FOR ALL USING (
  auth.uid() IN (SELECT user_id FROM conversations WHERE id = messages.conversation_id)
);

-- Data connections policies
CREATE POLICY "Users can manage own connections" ON data_connections FOR ALL USING (auth.uid() = user_id);

-- Wallet transaction policies
CREATE POLICY "Users can view own wallet transactions" ON wallet_transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own wallet transactions" ON wallet_transactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Team members can view team wallet transactions" ON wallet_transactions FOR SELECT USING (
  auth.uid() = user_id OR
  (team_id IS NOT NULL AND auth.uid() IN (SELECT user_id FROM team_members WHERE team_id = wallet_transactions.team_id))
);

-- Payment method policies
CREATE POLICY "Users can manage own payment methods" ON payment_methods FOR ALL USING (auth.uid() = user_id);

-- Affiliates policies
CREATE POLICY "Users can manage own affiliate data" ON affiliates FOR ALL USING (auth.uid() = user_id);

-- Affiliate commissions policies
CREATE POLICY "Affiliates can view own commissions" ON affiliate_commissions FOR SELECT USING (
  auth.uid() IN (SELECT user_id FROM affiliates WHERE id = affiliate_commissions.affiliate_id)
);

-- =====================================================
-- FUNCTIONS & TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_connections_updated_at BEFORE UPDATE ON data_connections
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to check wallet balance before query execution
CREATE OR REPLACE FUNCTION check_wallet_balance(user_uuid UUID, estimated_cost_cents INTEGER)
RETURNS boolean AS $$
DECLARE
  user_balance INTEGER;
BEGIN
  SELECT wallet_balance_cents INTO user_balance
  FROM users
  WHERE id = user_uuid;

  -- Check if user has sufficient balance
  RETURN user_balance >= estimated_cost_cents;
END;
$$ language 'plpgsql';

-- Function to process wallet transaction (atomic balance update)
CREATE OR REPLACE FUNCTION process_wallet_transaction(
  user_uuid UUID,
  amount_cents INTEGER,
  transaction_type TEXT,
  description TEXT,
  reference_id TEXT DEFAULT NULL,
  conversation_id UUID DEFAULT NULL,
  message_id UUID DEFAULT NULL,
  model_used TEXT DEFAULT NULL,
  tokens_used INTEGER DEFAULT 0,
  provider TEXT DEFAULT NULL,
  provider_transaction_id TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  current_balance INTEGER;
  new_balance INTEGER;
  transaction_id UUID;
BEGIN
  -- Get current balance with row lock
  SELECT wallet_balance_cents INTO current_balance
  FROM users
  WHERE id = user_uuid
  FOR UPDATE;

  -- Calculate new balance
  new_balance := current_balance + amount_cents;

  -- Ensure balance doesn't go negative
  IF new_balance < 0 THEN
    RAISE EXCEPTION 'Insufficient wallet balance. Current: %, Required: %', current_balance, ABS(amount_cents);
  END IF;

  -- Update user balance
  UPDATE users
  SET wallet_balance_cents = new_balance,
      total_spent_cents = CASE
        WHEN amount_cents < 0 THEN total_spent_cents + ABS(amount_cents)
        ELSE total_spent_cents
      END,
      total_loaded_cents = CASE
        WHEN amount_cents > 0 AND transaction_type = 'load' THEN total_loaded_cents + amount_cents
        ELSE total_loaded_cents
      END,
      total_queries_count = CASE
        WHEN transaction_type = 'spend' THEN total_queries_count + 1
        ELSE total_queries_count
      END,
      total_tokens_used = total_tokens_used + tokens_used,
      last_query_at = CASE
        WHEN transaction_type = 'spend' THEN NOW()
        ELSE last_query_at
      END,
      updated_at = NOW()
  WHERE id = user_uuid;

  -- Insert transaction record
  INSERT INTO wallet_transactions (
    user_id, transaction_type, amount_cents, balance_after_cents,
    description, reference_id, conversation_id, message_id,
    model_used, tokens_used, provider, provider_transaction_id,
    currency, status
  ) VALUES (
    user_uuid, transaction_type, amount_cents, new_balance,
    description, reference_id, conversation_id, message_id,
    model_used, tokens_used, provider, provider_transaction_id,
    'USD', 'completed'
  ) RETURNING id INTO transaction_id;

  RETURN transaction_id;
END;
$$ language 'plpgsql';

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default system data if needed
-- This would be handled by the application during first setup

-- Migration complete
SELECT 'Excella wallet-based database migration completed successfully' as status;
