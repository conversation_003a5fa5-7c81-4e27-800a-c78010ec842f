# General Documentation

This directory contains general documentation that is not specific to the Excella project.

## Directory Purpose

The `docs/` directory is reserved for:

- **General development documentation** (coding standards, workflows, etc.)
- **Generic technical guides** (not Excella-specific)
- **Team processes and procedures**
- **General API documentation** (if applicable)
- **Contributing guidelines**
- **Code of conduct**
- **License information**
- **General troubleshooting guides**

## Excella-Specific Documentation

For **Excella project-specific documentation**, please see the **`.excella/`** directory:

- **📖 Excella Documentation Index**: [`.excella/readme.md`](../.excella/readme.md)
- **🚀 Getting Started**: [`.excella/setup/package-installation-guide.md`](../.excella/setup/package-installation-guide.md)
- **🏗️ Technical Stack**: [`.excella/core/technical-stack.md`](../.excella/core/technical-stack.md)
- **📋 Project Brief**: [`.excella/specs/project-brief.md`](../.excella/specs/project-brief.md)
- **🔬 Research Findings**: [`.excella/research/`](../.excella/research/)

## Documentation Structure Overview

```
project-root/
├── docs/                        # General documentation (this directory)
│   ├── readme.md               # This file
│   ├── contributing.md         # Contributing guidelines
│   ├── code-of-conduct.md      # (future) Code of conduct
│   └── ...                     # Other general docs
└── .excella/                   # Excella-specific documentation
    ├── research-plan.md        # Overall research strategy and planning
    ├── core/                   # Core documentation, specs, and architecture
    ├── research/               # Research phases
    ├── setup/                  # Installation guides
    └── assets/                 # Project assets
```

## When to Use Which Directory

### Use `docs/` for:
- Documentation that could apply to any project
- General development practices
- Team workflows and processes
- Generic technical guides
- Open source project documentation (license, contributing, etc.)

### Use `.excella/` for:
- Excella project-specific content
- Technical stack decisions for Excella
- Excella research findings
- Excella requirements and project brief
- Excella setup and installation guides

## File Naming Convention

This project uses **kebab-case** for all file names:
- ✅ `readme.md`
- ✅ `contributing.md`
- ✅ `code-of-conduct.md`
- ❌ `README.md`
- ❌ `CONTRIBUTING.md`

---

*This separation ensures clear organization between general documentation and Excella-specific project content.*
