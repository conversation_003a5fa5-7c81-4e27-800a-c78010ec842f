{"name": "excella-monorepo", "version": "1.0.0", "description": "AI-Powered Excel Add-in - Monorepo", "type": "module", "private": true, "workspaces": ["apps/web-dashboard", "packages/*"], "scripts": {"build": "npm run build --workspaces", "dev": "npm run dev --workspaces", "lint": "npm run lint --workspaces", "test": "npm run test --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules", "install:all": "npm install", "excel-addin:dev": "npm run dev --workspace=apps/excel-addin", "web-dashboard:dev": "npm run dev --workspace=apps/web-dashboard", "excel-addin:build": "npm run build --workspace=apps/excel-addin", "web-dashboard:build": "npm run build --workspace=apps/web-dashboard", "prepare": "husky"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "typescript": "^5.6.0"}, "dependencies": {"framer-motion": "^11.18.2", "next": "^15.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^3.4.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c"}