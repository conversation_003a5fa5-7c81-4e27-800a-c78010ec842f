{"name": "@excella/ui", "version": "1.0.0", "description": "Shared UI components for Excella", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src", "lint:fix": "eslint src --fix", "test": "jest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "rm -rf dist"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.17", "framer-motion": "^11.18.2", "lucide-react": "^0.460.0", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "@excella/shared": "*"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.8.3", "@storybook/react": "^8.0.0", "@storybook/addon-essentials": "^8.0.0"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}